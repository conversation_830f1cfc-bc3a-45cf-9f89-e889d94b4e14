# BTC & ETH 日本蜡烛图交易策略

基于史蒂夫·尼森《日本蜡烛图技术新解》理论，专门为比特币(BTC)和以太坊(ETH)设计的自动化交易策略。

## 📚 理论基础

本策略基于日本蜡烛图技术分析，这是一种起源于18世纪日本米市的古老技术分析方法。史蒂夫·尼森将这一技术引入西方，并在《日本蜡烛图技术新解》中详细阐述了其应用方法。

### 核心理念

1. **蜡烛图形态识别**: 通过特定的蜡烛图组合预测价格走势
2. **趋势确认**: 结合市场趋势和成交量进行信号确认
3. **风险管理**: 严格的止损止盈和仓位管理
4. **多时间框架分析**: 结合不同时间周期提高准确性

## 🎯 支持的蜡烛图形态

### 单根蜡烛形态
- **锤子线 (Hammer)**: 底部反转信号
- **上吊线 (Hanging Man)**: 顶部反转信号
- **射击之星 (Shooting Star)**: 顶部反转信号
- **十字星 (Doji)**: 市场犹豫不决信号

### 多根蜡烛组合
- **看涨/看跌吞没 (Engulfing)**: 强烈的反转信号
- **启明星 (Morning Star)**: 强烈的底部反转信号
- **黄昏星 (Evening Star)**: 强烈的顶部反转信号

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install pandas numpy matplotlib seaborn

# 克隆或下载项目文件
# 确保以下文件在同一目录：
# - candlestick_pattern_detector.py
# - btc_eth_trading_bot.py
# - trading_config.py
```

### 2. 基本使用

```python
from btc_eth_trading_bot import BTCETHTradingBot
from trading_config import load_config

# 加载配置
config = load_config('conservative')  # 保守型配置

# 创建交易机器人
bot = BTCETHTradingBot(initial_balance=10000)

# 运行策略 (模拟模式)
bot.run_strategy()
```

### 3. 自定义配置

```python
from trading_config import TradingConfig

# 创建自定义配置
custom_config = TradingConfig(
    initial_balance=5000,
    max_position_size=0.03,
    min_confidence=0.7,
    symbols=['BTC/USDT', 'ETH/USDT'],
    timeframes=['1h', '4h']
)

# 使用自定义配置
bot = BTCETHTradingBot(custom_config)
```

## 📊 策略特点

### 风险管理
- **仓位控制**: 单笔交易不超过总资金的2%
- **止损设置**: 每笔交易都有明确的止损点
- **风险回报比**: 默认1:2的风险回报比
- **日亏损限制**: 日亏损不超过总资金的5%

### 信号确认机制
1. **蜡烛图形态确认**: 识别有效的蜡烛图形态
2. **趋势确认**: 结合移动平均线判断趋势方向
3. **支撑阻力确认**: 在关键价位附近的形态更有效
4. **成交量确认**: 形态伴随成交量放大更可靠
5. **技术指标确认**: RSI等指标提供额外确认

### 多时间框架分析
- **1小时图**: 短期交易信号
- **4小时图**: 中期趋势确认
- **日线图**: 长期趋势判断

## ⚙️ 配置选项

### 预设配置类型

1. **保守型 (Conservative)**
   - 低风险，高置信度要求
   - 适合新手和风险厌恶者

2. **激进型 (Aggressive)**
   - 高风险高回报
   - 适合经验丰富的交易者

3. **剥头皮 (Scalping)**
   - 短时间框架，快进快出
   - 适合日内交易

4. **波段交易 (Swing Trading)**
   - 长时间框架，持仓数天
   - 适合趋势跟随

### 主要参数

```python
# 风险管理参数
max_position_size = 0.02      # 最大仓位比例
max_daily_loss = 0.05         # 日最大亏损比例
risk_per_trade = 0.02         # 单笔交易风险

# 形态检测参数
min_confidence = 0.6          # 最小置信度
min_body_ratio = 0.1          # 最小实体比例
doji_threshold = 0.05         # 十字星阈值

# 技术指标参数
rsi_period = 14               # RSI周期
rsi_oversold = 30             # RSI超卖线
rsi_overbought = 70           # RSI超买线
```

## 📈 使用示例

### 示例1: 基本策略运行

```python
from btc_eth_trading_bot import BTCETHTradingBot

# 创建机器人
bot = BTCETHTradingBot(initial_balance=10000)

# 生成交易信号
signals = bot.generate_trading_signals('BTC/USDT')

# 查看信号
for signal in signals:
    print(f"信号: {signal.action} {signal.symbol} @ {signal.price}")
    print(f"置信度: {signal.confidence:.2f}")
    print(f"原因: {signal.reason}")
```

### 示例2: 形态检测

```python
from candlestick_pattern_detector import CandlestickPatternDetector
import pandas as pd

# 创建检测器
detector = CandlestickPatternDetector()

# 准备数据 (OHLCV格式)
df = pd.DataFrame({
    'open': [50000, 49800, 49500],
    'high': [50200, 50000, 49800],
    'low': [49500, 49300, 49000],
    'close': [49800, 49500, 49700],
    'volume': [1000, 1200, 800]
})

# 扫描形态
patterns = detector.scan_patterns(df)

# 输出结果
for pattern in patterns:
    print(f"发现形态: {pattern.description}")
    print(f"置信度: {pattern.confidence:.2f}")
```

## 🔧 高级功能

### 1. 自定义形态检测

```python
# 扩展检测器类
class CustomPatternDetector(CandlestickPatternDetector):
    def detect_custom_pattern(self, df, index):
        # 实现自定义形态检测逻辑
        pass
```

### 2. 实盘交易集成

```python
# 连接真实交易所API
class LiveTradingBot(BTCETHTradingBot):
    def __init__(self, api_key, api_secret):
        super().__init__()
        self.exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': True  # 测试环境
        })
    
    def get_market_data(self, symbol, timeframe):
        # 从交易所获取实时数据
        return self.exchange.fetch_ohlcv(symbol, timeframe)
```

### 3. 回测功能

```python
# 历史数据回测
def backtest_strategy(start_date, end_date, initial_balance):
    bot = BTCETHTradingBot(initial_balance)
    # 实现回测逻辑
    return results
```

## ⚠️ 重要提醒

1. **风险警告**: 加密货币交易存在高风险，可能导致资金损失
2. **模拟测试**: 建议先在模拟环境中测试策略
3. **参数调优**: 根据市场条件调整策略参数
4. **持续监控**: 策略运行期间需要持续监控
5. **资金管理**: 只投入可承受损失的资金

## 📝 日志和监控

策略运行时会生成详细的日志信息：

```
2024-01-01 10:00:00 - INFO - 发现锤子线形态: BTC/USDT @ 45000
2024-01-01 10:00:01 - INFO - 执行交易: BUY 0.1 BTC/USDT @ 45000
2024-01-01 10:30:00 - INFO - 当前余额: 9550.00, 持仓数量: 1
```

## 🤝 贡献和支持

欢迎提交问题报告、功能请求和代码贡献。

### 开发计划
- [ ] 添加更多蜡烛图形态
- [ ] 实现机器学习优化
- [ ] 添加情绪分析指标
- [ ] 开发Web界面
- [ ] 支持更多交易对

## 📄 许可证

本项目仅供学习和研究使用，不构成投资建议。

---

**免责声明**: 本策略基于技术分析理论，不保证盈利。投资有风险，入市需谨慎。
