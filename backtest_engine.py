"""
BTC & ETH 蜡烛图策略回测引擎
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from candlestick_pattern_detector import CandlestickPatternDetector, CandlestickPattern
from trading_config import TradingConfig, load_config

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.pattern_detector = CandlestickPatternDetector(
            min_body_ratio=config.min_body_ratio,
            doji_threshold=config.doji_threshold
        )
        
        # 回测结果
        self.trades = []
        self.equity_curve = []
        self.positions = {}
        self.balance = config.initial_balance
        self.initial_balance = config.initial_balance
        
        # 统计指标
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_profit = 0
        self.total_loss = 0
        self.max_drawdown = 0
        self.max_equity = config.initial_balance
        
    def get_market_data(self, symbol: str, start_date: str, end_date: str,
                       timeframe: str = '5m', use_real_data: bool = True) -> pd.DataFrame:
        """
        获取市场数据（真实数据或模拟数据）
        """
        if use_real_data:
            try:
                from data_fetcher import CryptoDataFetcher
                fetcher = CryptoDataFetcher()

                # 尝试从不同数据源获取真实数据，优先使用OKX
                sources = ['okx', 'binance', 'yahoo', 'coingecko']
                real_data_available = False
                
                for source in sources:
                    try:
                        print(f"尝试从 {source} 获取数据...")
                        df = fetcher.fetch_data(symbol, timeframe, start_date, end_date, source)
                        if not df.empty:
                            print(f"✅ 从 {source} 获取到 {symbol} 真实数据: {len(df)} 条记录")
                            real_data_available = True
                            return df
                        else:
                            print(f"⚠️ 从 {source} 获取的数据为空")
                    except Exception as e:
                        print(f"❌ 从 {source} 获取数据失败: {e}")
                        continue

                if not real_data_available:
                    print(f"⚠️ 所有数据源都无法获取 {symbol} 的真实数据")
                    print("📊 使用优化的模拟数据（基于真实市场特征）")
                    print("💡 提示：如需真实数据，请检查网络连接或使用VPN")

            except ImportError:
                print("⚠️ 数据获取模块未找到，使用模拟数据")

        # 使用改进的模拟数据生成
        return self.generate_realistic_data(symbol, start_date, end_date, timeframe)

    def generate_realistic_data(self, symbol: str, start_date: str, end_date: str,
                              timeframe: str = '5m') -> pd.DataFrame:
        """
        生成更真实的模拟数据
        """
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)

        # 根据时间框架确定频率
        freq_map = {'5m': '5T', '15m': '15T', '30m': '30T', '1h': 'H', '4h': '4H', '1d': 'D'}
        freq = freq_map.get(timeframe, '5T')

        dates = pd.date_range(start=start, end=end, freq=freq)
        n_periods = len(dates)

        # 基础价格（基于2023年实际价格水平）
        base_price = 42000 if 'BTC' in symbol else 2500

        # 生成更真实的价格走势
        # 1. 长期趋势（模拟2023年的市场走势）
        trend_periods = max(1, n_periods // 100)
        trend_changes = np.random.choice([-1, 0, 1], trend_periods, p=[0.3, 0.4, 0.3])
        trend = np.repeat(trend_changes, n_periods // trend_periods + 1)[:n_periods]
        trend = np.cumsum(trend) * 0.002  # 趋势强度

        # 2. 周期性波动（模拟市场周期）
        cycle = np.sin(np.linspace(0, 6*np.pi, n_periods)) * 0.08

        # 3. 随机波动（加密货币特有的高波动性）
        volatility = 0.04 if 'BTC' in symbol else 0.05
        noise = np.random.normal(0, volatility, n_periods)

        # 4. 跳跃（模拟重大事件影响）
        jump_prob = 0.002  # 降低跳跃概率
        jumps = np.random.choice([0, 1], n_periods, p=[1-jump_prob, jump_prob])
        jump_sizes = np.random.normal(0, 0.08, n_periods) * jumps  # 降低跳跃幅度

        # 5. 周末效应（加密货币市场24/7，但仍有一些模式）
        weekend_effect = np.where(pd.to_datetime(dates).dayofweek >= 5, 0.99, 1.0)

        # 合成价格变化
        returns = trend + cycle + noise + jump_sizes
        
        # 限制单日收益率范围，避免异常值
        returns = np.clip(returns, -0.2, 0.2)  # 限制在±20%范围内
        
        cumulative_returns = np.cumsum(returns)

        # 生成收盘价
        close_prices = base_price * np.exp(cumulative_returns) * weekend_effect
        
        # 确保价格在合理范围内
        close_prices = np.clip(close_prices, base_price * 0.1, base_price * 10)

        # 生成OHLC数据
        daily_vol = volatility / 3

        # 开盘价（基于前一期收盘价加上跳空）
        open_prices = np.zeros_like(close_prices)
        open_prices[0] = close_prices[0]
        for i in range(1, len(close_prices)):
            gap = np.random.normal(0, daily_vol/3)
            open_prices[i] = close_prices[i-1] * (1 + gap)

        # 最高价和最低价（确保逻辑正确）
        intraday_range = np.abs(np.random.normal(0, daily_vol, n_periods))
        # 限制日内波动范围，避免异常值
        intraday_range = np.clip(intraday_range, 0, 0.1)  # 最大10%日内波动
        
        high_prices = np.maximum(open_prices, close_prices) * (1 + intraday_range)
        low_prices = np.minimum(open_prices, close_prices) * (1 - intraday_range)

        # 成交量（与价格波动和趋势相关）
        base_volume = 45000 if 'BTC' in symbol else 180000
        volume_trend = 1 + np.abs(returns) * 8  # 波动越大，成交量越大
        volume_noise = np.random.lognormal(0, 0.6, n_periods)
        volumes = base_volume * volume_trend * volume_noise

        # 创建DataFrame
        df = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        }, index=dates)

        # 确保价格逻辑正确
        df['high'] = df[['open', 'high', 'low', 'close']].max(axis=1)
        df['low'] = df[['open', 'high', 'low', 'close']].min(axis=1)

        return df
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = df.copy()
        
        # 移动平均线
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # ATR (平均真实波幅) - 针对5分钟K线优化
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        
        # 对于5分钟K线，使用更短的ATR周期，提高响应性
        atr_period = 10 if '5m' in str(df.index.freq) or len(df) < 100 else 14
        df['atr'] = true_range.rolling(window=atr_period).mean()
        
        return df
    
    def identify_support_resistance(self, df: pd.DataFrame, window: int = 20) -> Tuple[pd.Series, pd.Series]:
        """识别支撑和阻力位"""
        # 滚动窗口内的最低点作为支撑
        support = df['low'].rolling(window=window, center=True).min()
        
        # 滚动窗口内的最高点作为阻力
        resistance = df['high'].rolling(window=window, center=True).max()
        
        return support, resistance
    
    def analyze_market_context(self, df: pd.DataFrame, index: int) -> Dict:
        """分析市场环境"""
        if index < 50:  # 需要足够的历史数据
            return {'trend': 'UNKNOWN', 'rsi_signal': 'NEUTRAL'}
        
        current_data = df.iloc[index]
        
        # 趋势判断
        if current_data['close'] > current_data['sma_20'] > current_data['sma_50']:
            trend = 'UPTREND'
        elif current_data['close'] < current_data['sma_20'] < current_data['sma_50']:
            trend = 'DOWNTREND'
        else:
            trend = 'SIDEWAYS'
        
        # RSI信号
        rsi = current_data['rsi']
        if rsi > 70:
            rsi_signal = 'OVERBOUGHT'
        elif rsi < 30:
            rsi_signal = 'OVERSOLD'
        else:
            rsi_signal = 'NEUTRAL'
        
        # 布林带位置
        close = current_data['close']
        bb_position = 'MIDDLE'
        if close > current_data['bb_upper']:
            bb_position = 'ABOVE_UPPER'
        elif close < current_data['bb_lower']:
            bb_position = 'BELOW_LOWER'
        
        return {
            'trend': trend,
            'rsi': rsi,
            'rsi_signal': rsi_signal,
            'bb_position': bb_position,
            'atr': current_data['atr'],
            'volume_ratio': current_data['volume'] / df['volume'].rolling(20).mean().iloc[index]
        }
    
    def generate_signals(self, df: pd.DataFrame, symbol: str) -> List[Dict]:
        """生成交易信号"""
        signals = []
        
        # 添加技术指标
        df = self.calculate_technical_indicators(df)
        
        # 识别支撑阻力
        support, resistance = self.identify_support_resistance(df)
        df['support'] = support
        df['resistance'] = resistance
        
        # 检测蜡烛图形态
        patterns = self.pattern_detector.scan_patterns(df)
        
        # 将形态转换为交易信号
        for pattern in patterns:
            try:
                # 找到对应的数据行
                pattern_index = df.index.get_loc(pattern.timestamp)
                
                if pattern_index < 50:  # 跳过数据不足的早期
                    continue
                
                # 分析市场环境
                market_context = self.analyze_market_context(df, pattern_index)
                
                # 生成信号
                signal = self._pattern_to_signal(pattern, market_context, df.iloc[pattern_index], symbol)
                if signal:
                    signals.append(signal)
            except Exception as e:
                print(f"⚠️ 生成信号时出错: {e}")
                continue
        
        return signals
    
    def _pattern_to_signal(self, pattern: CandlestickPattern, market_context: Dict, 
                          current_data: pd.Series, symbol: str) -> Optional[Dict]:
        """将形态转换为交易信号"""
        if pattern.confidence < self.config.min_confidence:
            return None
        
        current_price = current_data['close']
        atr = current_data['atr']
        
        # 看涨信号
        if pattern.bullish:
            # 确认条件
            confirmations = []
            
            # 趋势确认
            if market_context['trend'] in ['UPTREND', 'SIDEWAYS']:
                confirmations.append('trend_favorable')
            
            # RSI确认
            if market_context['rsi_signal'] == 'OVERSOLD':
                confirmations.append('rsi_oversold')
            
            # 布林带确认
            if market_context['bb_position'] == 'BELOW_LOWER':
                confirmations.append('bb_oversold')
            
            # 成交量确认
            if market_context['volume_ratio'] > 1.2:
                confirmations.append('volume_surge')
            
            # 需要至少一个确认
            if confirmations:
                # 计算止损止盈 - 智能优化版本
                # 根据市场波动性动态调整
                atr = current_data['atr']
                volatility_ratio = atr / current_price
                
                # 动态止损：波动性大时止损更宽，波动性小时止损更紧
                if volatility_ratio > 0.02:  # 高波动
                    stop_loss_pct = 0.02    # 2%止损
                    take_profit_pct = 0.04  # 4%止盈
                elif volatility_ratio > 0.01:  # 中等波动
                    stop_loss_pct = 0.015   # 1.5%止损
                    take_profit_pct = 0.035 # 3.5%止盈
                else:  # 低波动
                    stop_loss_pct = 0.012   # 1.2%止损
                    take_profit_pct = 0.03  # 3%止盈
                
                stop_loss = current_price * (1 - stop_loss_pct)
                take_profit = current_price * (1 + take_profit_pct)
                
                return {
                    'timestamp': pattern.timestamp,
                    'symbol': symbol,
                    'action': 'BUY',
                    'price': current_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'confidence': pattern.confidence,
                    'pattern_type': pattern.pattern_type.value,
                    'confirmations': confirmations,
                    'atr': atr
                }
        
        # 看跌信号
        elif pattern.bullish == False:
            confirmations = []
            
            if market_context['trend'] in ['DOWNTREND', 'SIDEWAYS']:
                confirmations.append('trend_favorable')
            
            if market_context['rsi_signal'] == 'OVERBOUGHT':
                confirmations.append('rsi_overbought')
            
            if market_context['bb_position'] == 'ABOVE_UPPER':
                confirmations.append('bb_overbought')
            
            if market_context['volume_ratio'] > 1.2:
                confirmations.append('volume_surge')
            
            if confirmations:
                # 计算止损止盈 - 智能优化版本（空头）
                # 根据市场波动性动态调整
                atr = current_data['atr']
                volatility_ratio = atr / current_price
                
                # 动态止损：波动性大时止损更宽，波动性小时止损更紧
                if volatility_ratio > 0.02:  # 高波动
                    stop_loss_pct = 0.02    # 2%止损
                    take_profit_pct = 0.04  # 4%止盈
                elif volatility_ratio > 0.01:  # 中等波动
                    stop_loss_pct = 0.015   # 1.5%止损
                    take_profit_pct = 0.035 # 3.5%止盈
                else:  # 低波动
                    stop_loss_pct = 0.012   # 1.2%止损
                    take_profit_pct = 0.03  # 3%止盈
                
                stop_loss = current_price * (1 + stop_loss_pct)
                take_profit = current_price * (1 - take_profit_pct)
                
                return {
                    'timestamp': pattern.timestamp,
                    'symbol': symbol,
                    'action': 'SELL',
                    'price': current_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'confidence': pattern.confidence,
                    'pattern_type': pattern.pattern_type.value,
                    'confirmations': confirmations,
                    'atr': atr
                }
        
        return None
    
    def calculate_position_size(self, signal: Dict) -> float:
        """计算仓位大小 - 智能优化版本"""
        risk_amount = self.balance * self.config.risk_per_trade
        price_risk = abs(signal['price'] - signal['stop_loss'])
        
        if price_risk == 0:
            return 0
        
        # 基础仓位计算
        position_size = risk_amount / price_risk
        
        # 根据信号质量调整仓位
        confidence_multiplier = 1.0
        if signal['confidence'] >= 0.8:
            confidence_multiplier = 1.2  # 高置信度信号增加仓位
        elif signal['confidence'] >= 0.7:
            confidence_multiplier = 1.0  # 中等置信度信号保持标准仓位
        else:
            confidence_multiplier = 0.8  # 低置信度信号减少仓位
        
        # 根据市场环境调整仓位
        market_multiplier = 1.0
        if len(signal.get('confirmations', [])) >= 3:
            market_multiplier = 1.1  # 多重确认增加仓位
        elif len(signal.get('confirmations', [])) <= 1:
            market_multiplier = 0.9  # 确认不足减少仓位
        
        # 应用调整因子
        position_size *= confidence_multiplier * market_multiplier
        
        # 限制最大仓位
        max_position_value = self.balance * self.config.max_position_size
        max_size = max_position_value / signal['price']
        
        return min(position_size, max_size)
    
    def execute_trade(self, signal: Dict, df: pd.DataFrame) -> bool:
        """执行交易"""
        position_size = self.calculate_position_size(signal)
        
        if position_size <= 0:
            return False
        
        # 检查是否已有持仓
        if signal['symbol'] in self.positions:
            return False  # 已有持仓，跳过
        
        # 计算交易成本（支持合约：按杠杆占用保证金）
        trade_value = signal['price'] * position_size
        commission_rate = getattr(self.config, 'commission_rate', 0.001)
        leverage = getattr(self.config, 'leverage', 1.0) if getattr(self.config, 'futures_enabled', False) else 1.0
        if leverage <= 0:
            leverage = 1.0
        required_margin = trade_value / leverage
        open_commission = trade_value * commission_rate
        total_required = required_margin + open_commission
        
        if total_required > self.balance:
            return False  # 资金不足
        
        # 扣减保证金与开仓手续费
        self.balance -= total_required
        
        # 执行交易
        self.positions[signal['symbol']] = {
            'side': 'LONG' if signal['action'] == 'BUY' else 'SHORT',
            'entry_price': signal['price'],
            'size': position_size,
            'stop_loss': signal['stop_loss'],
            'take_profit': signal['take_profit'],
            'entry_time': signal['timestamp'],
            'pattern_type': signal['pattern_type'],
            'confidence': signal['confidence'],
            'leverage': leverage,
            'open_commission': open_commission,
            'required_margin': required_margin
        }
        
        return True
    
    def check_exit_conditions(self, df: pd.DataFrame, current_time: pd.Timestamp):
        """检查平仓条件（包含移动止盈止损）"""
        # 修复：使用正确的方式访问时间戳对应的数据
        try:
            # 如果DataFrame的索引是时间戳类型
            if isinstance(df.index, pd.DatetimeIndex):
                current_data = df.loc[current_time]
            else:
                # 如果索引不是时间戳，尝试找到最接近的时间
                current_data = df.iloc[-1]  # 使用最后一行数据作为当前数据
            
            current_price = current_data['close']
        except (KeyError, IndexError):
            # 如果无法找到对应时间的数据，跳过这次检查
            return
        
        positions_to_close = []
        
        for symbol, position in self.positions.items():
            # 修复：简化符号匹配逻辑
            if symbol not in str(current_data.name):  # 转换为字符串进行比较
                continue
            
            should_close = False
            exit_reason = ""
            
            # 更新移动止盈止损
            self._update_trailing_stops(position, current_price)
            
            if position['side'] == 'LONG':
                if current_price <= position['stop_loss']:
                    should_close = True
                    exit_reason = "stop_loss"
                elif current_price >= position['take_profit']:
                    should_close = True
                    exit_reason = "take_profit"
            else:  # SHORT
                if current_price >= position['stop_loss']:
                    should_close = True
                    exit_reason = "stop_loss"
                elif current_price <= position['take_profit']:
                    should_close = True
                    exit_reason = "take_profit"
            
            if should_close:
                positions_to_close.append((symbol, current_price, exit_reason))
                print(f"🔍 触发{exit_reason}: {symbol} @ ${current_price:.2f}")
                print(f"   止损价: ${position['stop_loss']:.2f}, 止盈价: ${position['take_profit']:.2f}")
                print(f"   入场价: ${position['entry_price']:.2f}")
        
        # 执行平仓
        for symbol, exit_price, reason in positions_to_close:
            self._close_position(symbol, exit_price, current_time, reason)
    
    def _update_trailing_stops(self, position: Dict, current_price: float):
        """更新移动止盈止损"""
        if not getattr(self.config, 'trailing_stop_enabled', False):
            return
        
        trailing_stop_pct = getattr(self.config, 'trailing_stop_pct', 0.01)
        trailing_take_profit_pct = getattr(self.config, 'trailing_take_profit_pct', 0.005)
        
        if position['side'] == 'LONG':
            # 多头移动止损：价格上升时，止损价也上升
            new_stop_loss = current_price * (1 - trailing_stop_pct)
            if new_stop_loss > position['stop_loss']:
                position['stop_loss'] = new_stop_loss
                print(f"📈 多头移动止损更新: ${new_stop_loss:.2f}")
            
            # 多头移动止盈：价格上升时，止盈价也上升
            new_take_profit = current_price * (1 + trailing_take_profit_pct)
            if new_take_profit > position['take_profit']:
                position['take_profit'] = new_take_profit
                print(f"📈 多头移动止盈更新: ${new_take_profit:.2f}")
                
        else:  # SHORT
            # 空头移动止损：价格下降时，止损价也下降
            new_stop_loss = current_price * (1 + trailing_stop_pct)
            if new_stop_loss < position['stop_loss']:
                position['stop_loss'] = new_stop_loss
                print(f"📉 空头移动止损更新: ${new_stop_loss:.2f}")
            
            # 空头移动止盈：价格下降时，止盈价也下降
            new_take_profit = current_price * (1 - trailing_take_profit_pct)
            if new_take_profit < position['take_profit']:
                position['take_profit'] = new_take_profit
                print(f"📉 空头移动止盈更新: ${new_take_profit:.2f}")
    
    def _close_position(self, symbol: str, exit_price: float, exit_time: pd.Timestamp, reason: str):
        """平仓"""
        position = self.positions[symbol]
        
        # 计算盈亏
        if position['side'] == 'LONG':
            pnl = (exit_price - position['entry_price']) * position['size']
        else:
            pnl = (position['entry_price'] - exit_price) * position['size']
        
        # 平仓手续费与保证金释放
        commission_rate = getattr(self.config, 'commission_rate', 0.001)
        close_commission = exit_price * position['size'] * commission_rate
        gross_pnl = pnl
        net_pnl = gross_pnl - position.get('open_commission', 0) - close_commission
        
        # 释放保证金并更新余额
        self.balance += position.get('required_margin', 0)
        self.balance += net_pnl
        
        # 记录交易
        trade_record = {
            'symbol': symbol,
            'side': position['side'],
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'size': position['size'],
            'entry_time': position['entry_time'],
            'exit_time': exit_time,
            'pnl': net_pnl,
            'return_pct': net_pnl / (position['entry_price'] * position['size']) * 100,
            'exit_reason': reason,
            'pattern_type': position['pattern_type'],
            'confidence': position['confidence'],
            'duration': exit_time - position['entry_time']
        }
        
        self.trades.append(trade_record)
        
        # 更新统计
        self.total_trades += 1
        if net_pnl > 0:
            self.winning_trades += 1
            self.total_profit += net_pnl
        else:
            self.losing_trades += 1
            self.total_loss += abs(net_pnl)
        
        # 删除持仓
        del self.positions[symbol]
    
    def run_backtest(self, symbols: List[str], start_date: str, end_date: str, timeframe: str = '5m') -> Dict:
        """运行回测"""
        print(f"开始回测: {start_date} 到 {end_date}")
        print(f"交易对: {symbols}")
        print(f"初始资金: ${self.initial_balance:,.2f}")
        print("-" * 50)
        
        # 为每个交易对生成数据和信号
        all_signals = []
        data_dict = {}
        
        for symbol in symbols:
            print(f"获取 {symbol} 数据...")
            df = self.get_market_data(symbol, start_date, end_date, timeframe, use_real_data=True)
            data_dict[symbol] = df

            print(f"分析 {symbol} 信号...")
            signals = self.generate_signals(df, symbol)
            all_signals.extend(signals)
            print(f"发现 {len(signals)} 个信号")
        
        # 按时间排序信号
        all_signals.sort(key=lambda x: x['timestamp'])
        
        print(f"\n总共发现 {len(all_signals)} 个交易信号")
        print("开始模拟交易...")
        
        # 模拟交易
        for signal in all_signals:
            symbol = signal['symbol']
            df = data_dict[symbol]
            
            # 执行交易
            if self.execute_trade(signal, df):
                print(f"{signal['timestamp']}: {signal['action']} {symbol} @ ${signal['price']:.2f} "
                      f"({signal['pattern_type']}, 置信度: {signal['confidence']:.2f})")
            
            # 检查平仓条件
            self.check_exit_conditions(df, signal['timestamp'])
            
            # 记录权益曲线
            self.equity_curve.append({
                'timestamp': signal['timestamp'],
                'balance': self.balance,
                'positions_value': sum([pos['size'] * signal['price'] for pos in self.positions.values()]),
                'total_equity': self.balance + sum([pos['size'] * signal['price'] for pos in self.positions.values()])
            })
            
            # 更新最大权益和回撤
            current_equity = self.equity_curve[-1]['total_equity']
            if current_equity > self.max_equity:
                self.max_equity = current_equity
            
            drawdown = (self.max_equity - current_equity) / self.max_equity
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
        
        # 检查剩余持仓的止盈止损
        print(f"\n🔍 检查剩余持仓状态...")
        for symbol in list(self.positions.keys()):
            df = data_dict[symbol]
            position = self.positions[symbol]
            
            # 获取最新价格
            final_price = df['close'].iloc[-1]
            current_time = df.index[-1]
            
            print(f"   持仓: {symbol} {position['side']}")
            print(f"   入场价: ${position['entry_price']:.2f}")
            print(f"   当前价: ${final_price:.2f}")
            print(f"   止损价: ${position['stop_loss']:.2f}")
            print(f"   止盈价: ${position['take_profit']:.2f}")
            
            # 检查是否达到止盈止损
            should_close = False
            exit_reason = ""
            
            if position['side'] == 'LONG':
                if final_price <= position['stop_loss']:
                    should_close = True
                    exit_reason = "stop_loss"
                elif final_price >= position['take_profit']:
                    should_close = True
                    exit_reason = "take_profit"
            else:  # SHORT
                if final_price >= position['stop_loss']:
                    should_close = True
                    exit_reason = "stop_loss"
                elif final_price <= position['take_profit']:
                    should_close = True
                    exit_reason = "take_profit"
            
            if should_close:
                print(f"   ✅ 触发{exit_reason}，平仓")
                self._close_position(symbol, final_price, current_time, exit_reason)
            else:
                print(f"   ⏳ 未达到止盈止损条件，强制平仓")
                self._close_position(symbol, final_price, current_time, "backtest_end")
        
        return self.calculate_performance_metrics()
    
    def calculate_performance_metrics(self) -> Dict:
        """计算绩效指标"""
        if not self.trades:
            # 返回默认的绩效指标，避免KeyError
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_return': 0.0,
                'final_balance': self.balance,
                'max_drawdown': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'sharpe_ratio': 0.0,
                'avg_duration': pd.Timedelta(0),
                'total_profit': 0.0,
                'total_loss': 0.0
            }
        
        trades_df = pd.DataFrame(self.trades)
        
        # 基本指标
        total_return = (self.balance - self.initial_balance) / self.initial_balance * 100
        win_rate = self.winning_trades / self.total_trades * 100 if self.total_trades > 0 else 0
        
        # 平均盈亏
        avg_win = self.total_profit / self.winning_trades if self.winning_trades > 0 else 0
        avg_loss = self.total_loss / self.losing_trades if self.losing_trades > 0 else 0
        profit_factor = self.total_profit / self.total_loss if self.total_loss > 0 else float('inf')
        
        # 夏普比率 (简化计算)
        returns = trades_df['return_pct'].values
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        
        # 平均持仓时间
        avg_duration = trades_df['duration'].mean()
        
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'final_balance': self.balance,
            'max_drawdown': self.max_drawdown * 100,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'sharpe_ratio': sharpe_ratio,
            'avg_duration': avg_duration,
            'total_profit': self.total_profit,
            'total_loss': self.total_loss
        }

    def plot_results(self):
        """绘制回测结果图表"""
        if not self.trades or not self.equity_curve:
            print("没有数据可以绘制")
            return

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('BTC & ETH 蜡烛图策略回测结果', fontsize=16)

        # 1. 权益曲线
        equity_df = pd.DataFrame(self.equity_curve)
        axes[0, 0].plot(equity_df['timestamp'], equity_df['total_equity'], 'b-', linewidth=2)
        axes[0, 0].axhline(y=self.initial_balance, color='r', linestyle='--', alpha=0.7, label='初始资金')
        axes[0, 0].set_title('权益曲线')
        axes[0, 0].set_ylabel('总权益 ($)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 交易分布
        trades_df = pd.DataFrame(self.trades)
        profit_trades = trades_df[trades_df['pnl'] > 0]['pnl']
        loss_trades = trades_df[trades_df['pnl'] <= 0]['pnl']

        axes[0, 1].hist([profit_trades, loss_trades], bins=20, alpha=0.7,
                       color=['green', 'red'], label=['盈利交易', '亏损交易'])
        axes[0, 1].set_title('交易盈亏分布')
        axes[0, 1].set_xlabel('盈亏 ($)')
        axes[0, 1].set_ylabel('交易次数')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 月度收益
        trades_df['month'] = pd.to_datetime(trades_df['exit_time']).dt.to_period('M')
        monthly_pnl = trades_df.groupby('month')['pnl'].sum()

        colors = ['green' if x > 0 else 'red' for x in monthly_pnl.values]
        axes[1, 0].bar(range(len(monthly_pnl)), monthly_pnl.values, color=colors, alpha=0.7)
        axes[1, 0].set_title('月度盈亏')
        axes[1, 0].set_xlabel('月份')
        axes[1, 0].set_ylabel('盈亏 ($)')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 形态表现
        pattern_performance = trades_df.groupby('pattern_type').agg({
            'pnl': ['sum', 'mean', 'count']
        }).round(2)

        pattern_names = pattern_performance.index
        pattern_profits = pattern_performance[('pnl', 'sum')].values

        colors = ['green' if x > 0 else 'red' for x in pattern_profits]
        axes[1, 1].bar(range(len(pattern_names)), pattern_profits, color=colors, alpha=0.7)
        axes[1, 1].set_title('各形态表现')
        axes[1, 1].set_xlabel('蜡烛图形态')
        axes[1, 1].set_ylabel('总盈亏 ($)')
        axes[1, 1].set_xticks(range(len(pattern_names)))
        axes[1, 1].set_xticklabels(pattern_names, rotation=45, ha='right')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        return fig

if __name__ == "__main__":
    # 运行回测示例
    config = load_config('conservative')
    engine = BacktestEngine(config)

    results = engine.run_backtest(
        symbols=['BTC/USDT', 'ETH/USDT'],
        start_date='2023-01-01',
        end_date='2024-01-01'
    )

    print("\n" + "="*50)
    print("回测结果")
    print("="*50)
    for key, value in results.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")

    # 绘制结果图表
    print("\n正在生成图表...")
    engine.plot_results()
