"""
BTC & ETH 蜡烛图交易机器人
基于日本蜡烛图技术的自动化交易策略
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from candlestick_pattern_detector import CandlestickPatternDetector, CandlestickPattern, PatternType

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    price: float
    quantity: float
    stop_loss: float
    take_profit: float
    confidence: float
    reason: str
    timestamp: datetime

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    side: str  # 'LONG', 'SHORT'
    entry_price: float
    quantity: float
    stop_loss: float
    take_profit: float
    entry_time: datetime
    unrealized_pnl: float = 0.0

class RiskManager:
    """风险管理器"""
    
    def __init__(self, max_position_size: float = 0.02, max_daily_loss: float = 0.05):
        """
        初始化风险管理器
        
        Args:
            max_position_size: 单笔交易最大仓位比例
            max_daily_loss: 日最大亏损比例
        """
        self.max_position_size = max_position_size
        self.max_daily_loss = max_daily_loss
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.last_reset_date = datetime.now().date()
    
    def check_risk_limits(self, account_balance: float, proposed_trade_size: float) -> bool:
        """检查风险限制"""
        # 重置日统计
        if datetime.now().date() != self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.last_reset_date = datetime.now().date()
        
        # 检查单笔交易大小
        if proposed_trade_size > account_balance * self.max_position_size:
            logger.warning(f"交易大小超过限制: {proposed_trade_size} > {account_balance * self.max_position_size}")
            return False
        
        # 检查日亏损限制
        if self.daily_pnl < -account_balance * self.max_daily_loss:
            logger.warning(f"日亏损超过限制: {self.daily_pnl} < {-account_balance * self.max_daily_loss}")
            return False
        
        return True
    
    def calculate_position_size(self, account_balance: float, entry_price: float, 
                              stop_loss: float, risk_per_trade: float = 0.02) -> float:
        """计算仓位大小"""
        risk_amount = account_balance * risk_per_trade
        price_risk = abs(entry_price - stop_loss)
        
        if price_risk == 0:
            return 0
        
        position_size = risk_amount / price_risk
        max_size = account_balance * self.max_position_size / entry_price
        
        return min(position_size, max_size)

class BTCETHTradingBot:
    """BTC & ETH 交易机器人"""
    
    def __init__(self, initial_balance: float = 10000):
        """
        初始化交易机器人
        
        Args:
            initial_balance: 初始资金
        """
        self.balance = initial_balance
        self.initial_balance = initial_balance
        self.positions: Dict[str, Position] = {}
        self.pattern_detector = CandlestickPatternDetector()
        self.risk_manager = RiskManager()
        self.trade_history: List[TradingSignal] = []
        
        # 交易参数
        self.symbols = ['BTC/USDT', 'ETH/USDT']
        self.timeframes = ['1h', '4h', '1d']
        self.min_confidence = 0.6
        
        # 技术指标参数
        self.rsi_period = 14
        self.rsi_oversold = 30
        self.rsi_overbought = 70
    
    def get_market_data(self, symbol: str, timeframe: str, limit: int = 100) -> pd.DataFrame:
        """
        获取市场数据 (模拟函数，实际应连接交易所API)
        
        Returns:
            包含OHLCV数据的DataFrame
        """
        # 这里应该连接实际的交易所API
        # 现在返回模拟数据
        dates = pd.date_range(end=datetime.now(), periods=limit, freq='1H')
        base_price = 50000 if 'BTC' in symbol else 3000
        
        data = {
            'timestamp': dates,
            'open': np.random.randn(limit).cumsum() + base_price,
            'high': np.random.randn(limit).cumsum() + base_price + 100,
            'low': np.random.randn(limit).cumsum() + base_price - 100,
            'close': np.random.randn(limit).cumsum() + base_price,
            'volume': np.random.randint(1000, 10000, limit)
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def identify_support_resistance(self, df: pd.DataFrame, window: int = 20) -> Tuple[float, float]:
        """识别支撑和阻力位"""
        recent_data = df.tail(window)
        support = recent_data['low'].min()
        resistance = recent_data['high'].max()
        return support, resistance
    
    def analyze_market_context(self, df: pd.DataFrame) -> Dict:
        """分析市场环境"""
        current_price = df['close'].iloc[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        rsi = self.calculate_rsi(df['close']).iloc[-1]
        
        # 判断趋势
        if current_price > sma_20 > sma_50:
            trend = 'UPTREND'
        elif current_price < sma_20 < sma_50:
            trend = 'DOWNTREND'
        else:
            trend = 'SIDEWAYS'
        
        # 判断超买超卖
        if rsi > self.rsi_overbought:
            rsi_signal = 'OVERBOUGHT'
        elif rsi < self.rsi_oversold:
            rsi_signal = 'OVERSOLD'
        else:
            rsi_signal = 'NEUTRAL'
        
        support, resistance = self.identify_support_resistance(df)
        
        return {
            'trend': trend,
            'rsi': rsi,
            'rsi_signal': rsi_signal,
            'current_price': current_price,
            'sma_20': sma_20,
            'sma_50': sma_50,
            'support': support,
            'resistance': resistance
        }
    
    def generate_trading_signals(self, symbol: str) -> List[TradingSignal]:
        """生成交易信号"""
        signals = []
        
        # 获取不同时间框架的数据
        for timeframe in self.timeframes:
            df = self.get_market_data(symbol, timeframe)
            
            # 检测蜡烛图形态
            patterns = self.pattern_detector.scan_patterns(df)
            
            # 分析市场环境
            market_context = self.analyze_market_context(df)
            
            # 处理最近的形态
            for pattern in patterns[-3:]:  # 只看最近3个形态
                if pattern.confidence < self.min_confidence:
                    continue
                
                signal = self._pattern_to_signal(pattern, market_context, symbol, timeframe)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _pattern_to_signal(self, pattern: CandlestickPattern, market_context: Dict, 
                          symbol: str, timeframe: str) -> Optional[TradingSignal]:
        """将蜡烛图形态转换为交易信号"""
        current_price = market_context['current_price']
        support = market_context['support']
        resistance = market_context['resistance']
        
        # 看涨形态
        if pattern.bullish and pattern.pattern_type in [PatternType.HAMMER, PatternType.BULLISH_ENGULFING, PatternType.MORNING_STAR]:
            # 确认条件
            confirmations = []
            
            # 价格接近支撑位
            if abs(current_price - support) / current_price < 0.02:
                confirmations.append("接近支撑位")
            
            # RSI超卖
            if market_context['rsi_signal'] == 'OVERSOLD':
                confirmations.append("RSI超卖")
            
            # 至少需要一个确认条件
            if confirmations:
                stop_loss = support * 0.98  # 支撑位下方2%
                take_profit = current_price + (current_price - stop_loss) * 2  # 1:2风险回报比
                
                quantity = self.risk_manager.calculate_position_size(
                    self.balance, current_price, stop_loss
                )
                
                return TradingSignal(
                    symbol=symbol,
                    action='BUY',
                    price=current_price,
                    quantity=quantity,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    confidence=pattern.confidence,
                    reason=f"{pattern.description} + {', '.join(confirmations)}",
                    timestamp=datetime.now()
                )
        
        # 看跌形态
        elif pattern.bullish == False and pattern.pattern_type in [PatternType.SHOOTING_STAR, PatternType.BEARISH_ENGULFING, PatternType.EVENING_STAR]:
            # 确认条件
            confirmations = []
            
            # 价格接近阻力位
            if abs(current_price - resistance) / current_price < 0.02:
                confirmations.append("接近阻力位")
            
            # RSI超买
            if market_context['rsi_signal'] == 'OVERBOUGHT':
                confirmations.append("RSI超买")
            
            if confirmations:
                stop_loss = resistance * 1.02  # 阻力位上方2%
                take_profit = current_price - (stop_loss - current_price) * 2
                
                quantity = self.risk_manager.calculate_position_size(
                    self.balance, current_price, stop_loss
                )
                
                return TradingSignal(
                    symbol=symbol,
                    action='SELL',
                    price=current_price,
                    quantity=quantity,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    confidence=pattern.confidence,
                    reason=f"{pattern.description} + {', '.join(confirmations)}",
                    timestamp=datetime.now()
                )
        
        return None
    
    def execute_signal(self, signal: TradingSignal) -> bool:
        """执行交易信号"""
        # 检查风险限制
        trade_value = signal.price * signal.quantity
        if not self.risk_manager.check_risk_limits(self.balance, trade_value):
            logger.warning(f"风险检查失败，跳过交易: {signal.symbol} {signal.action}")
            return False
        
        # 检查是否已有相同方向的持仓
        if signal.symbol in self.positions:
            existing_position = self.positions[signal.symbol]
            if (signal.action == 'BUY' and existing_position.side == 'LONG') or \
               (signal.action == 'SELL' and existing_position.side == 'SHORT'):
                logger.info(f"已有相同方向持仓，跳过: {signal.symbol}")
                return False
        
        # 执行交易 (这里应该连接实际的交易所API)
        logger.info(f"执行交易: {signal.action} {signal.quantity} {signal.symbol} @ {signal.price}")
        
        # 更新持仓
        side = 'LONG' if signal.action == 'BUY' else 'SHORT'
        self.positions[signal.symbol] = Position(
            symbol=signal.symbol,
            side=side,
            entry_price=signal.price,
            quantity=signal.quantity,
            stop_loss=signal.stop_loss,
            take_profit=signal.take_profit,
            entry_time=signal.timestamp
        )
        
        # 更新余额 (简化处理)
        if signal.action == 'BUY':
            self.balance -= trade_value
        
        # 记录交易历史
        self.trade_history.append(signal)
        
        return True
    
    def check_exit_conditions(self):
        """检查平仓条件"""
        for symbol, position in list(self.positions.items()):
            current_data = self.get_market_data(symbol, '1h', 1)
            current_price = current_data['close'].iloc[-1]
            
            # 更新未实现盈亏
            if position.side == 'LONG':
                position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
            else:
                position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
            
            # 检查止损止盈
            should_exit = False
            exit_reason = ""
            
            if position.side == 'LONG':
                if current_price <= position.stop_loss:
                    should_exit = True
                    exit_reason = "止损"
                elif current_price >= position.take_profit:
                    should_exit = True
                    exit_reason = "止盈"
            else:
                if current_price >= position.stop_loss:
                    should_exit = True
                    exit_reason = "止损"
                elif current_price <= position.take_profit:
                    should_exit = True
                    exit_reason = "止盈"
            
            if should_exit:
                self._close_position(symbol, current_price, exit_reason)
    
    def _close_position(self, symbol: str, exit_price: float, reason: str):
        """平仓"""
        position = self.positions[symbol]
        
        # 计算盈亏
        if position.side == 'LONG':
            pnl = (exit_price - position.entry_price) * position.quantity
            self.balance += position.entry_price * position.quantity + pnl
        else:
            pnl = (position.entry_price - exit_price) * position.quantity
            self.balance += pnl
        
        logger.info(f"平仓: {symbol} {position.side} @ {exit_price}, 盈亏: {pnl:.2f}, 原因: {reason}")
        
        # 更新风险管理器
        self.risk_manager.daily_pnl += pnl
        
        # 删除持仓
        del self.positions[symbol]
    
    def run_strategy(self):
        """运行交易策略"""
        logger.info("开始运行BTC & ETH蜡烛图交易策略")
        
        while True:
            try:
                # 检查平仓条件
                self.check_exit_conditions()
                
                # 为每个交易对生成信号
                for symbol in self.symbols:
                    signals = self.generate_trading_signals(symbol)
                    
                    # 执行最佳信号
                    if signals:
                        best_signal = max(signals, key=lambda x: x.confidence)
                        self.execute_signal(best_signal)
                
                # 输出状态
                logger.info(f"当前余额: {self.balance:.2f}, 持仓数量: {len(self.positions)}")
                
                # 等待下一次检查 (实际应用中可能是几分钟到几小时)
                import time
                time.sleep(300)  # 5分钟
                
            except KeyboardInterrupt:
                logger.info("策略停止")
                break
            except Exception as e:
                logger.error(f"策略运行错误: {e}")
                import time
                time.sleep(60)  # 出错后等待1分钟

# 使用示例
if __name__ == "__main__":
    bot = BTCETHTradingBot(initial_balance=10000)
    bot.run_strategy()
