"""
BTC & ETH 日本蜡烛图形态识别器
基于史蒂夫·尼森的蜡烛图技术理论实现
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class PatternType(Enum):
    """蜡烛图形态类型"""
    HAMMER = "hammer"
    HANGING_MAN = "hanging_man"
    SHOOTING_STAR = "shooting_star"
    DOJI = "doji"
    BULLISH_ENGULFING = "bullish_engulfing"
    BEARISH_ENGULFING = "bearish_engulfing"
    MORNING_STAR = "morning_star"
    EVENING_STAR = "evening_star"
    SPINNING_TOP = "spinning_top"

class SignalStrength(Enum):
    """信号强度"""
    WEAK = 1
    MODERATE = 2
    STRONG = 3

@dataclass
class CandlestickPattern:
    """蜡烛图形态数据结构"""
    pattern_type: PatternType
    signal_strength: SignalStrength
    bullish: bool
    timestamp: pd.Timestamp
    price: float
    confidence: float
    description: str

class CandlestickPatternDetector:
    """蜡烛图形态检测器"""
    
    def __init__(self, min_body_ratio: float = 0.1, doji_threshold: float = 0.05):
        """
        初始化检测器
        
        Args:
            min_body_ratio: 最小实体比例
            doji_threshold: 十字星判定阈值
        """
        self.min_body_ratio = min_body_ratio
        self.doji_threshold = doji_threshold
    
    def analyze_candle(self, open_price: float, high: float, low: float, close: float) -> Dict:
        """
        分析单根蜡烛的基本属性
        
        Returns:
            包含蜡烛属性的字典
        """
        body = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        total_range = high - low
        
        return {
            'body': body,
            'upper_shadow': upper_shadow,
            'lower_shadow': lower_shadow,
            'total_range': total_range,
            'body_ratio': body / total_range if total_range > 0 else 0,
            'upper_shadow_ratio': upper_shadow / total_range if total_range > 0 else 0,
            'lower_shadow_ratio': lower_shadow / total_range if total_range > 0 else 0,
            'is_bullish': close > open_price,
            'is_bearish': close < open_price,
            'is_doji': body / total_range < self.doji_threshold if total_range > 0 else True
        }
    
    def detect_hammer(self, df: pd.DataFrame, index: int) -> Optional[CandlestickPattern]:
        """检测锤子线形态"""
        if index < 2:  # 需要前期下跌趋势
            return None
            
        current = df.iloc[index]
        candle_info = self.analyze_candle(current['open'], current['high'], 
                                        current['low'], current['close'])
        
        # 锤子线条件
        conditions = [
            candle_info['lower_shadow_ratio'] >= 0.6,  # 下影线至少占60%
            candle_info['upper_shadow_ratio'] <= 0.1,  # 上影线很短
            candle_info['body_ratio'] <= 0.3,  # 实体较小
            self._is_downtrend(df, index, 2)  # 前期下跌趋势
        ]
        
        if all(conditions):
            confidence = self._calculate_confidence(candle_info, 'hammer')
            return CandlestickPattern(
                pattern_type=PatternType.HAMMER,
                signal_strength=SignalStrength.MODERATE,
                bullish=True,
                timestamp=current.name,
                price=current['close'],
                confidence=confidence,
                description="锤子线 - 潜在底部反转信号"
            )
        return None
    
    def detect_shooting_star(self, df: pd.DataFrame, index: int) -> Optional[CandlestickPattern]:
        """检测射击之星形态"""
        if index < 2:
            return None
            
        current = df.iloc[index]
        candle_info = self.analyze_candle(current['open'], current['high'], 
                                        current['low'], current['close'])
        
        # 射击之星条件
        conditions = [
            candle_info['upper_shadow_ratio'] >= 0.6,  # 上影线至少占60%
            candle_info['lower_shadow_ratio'] <= 0.1,  # 下影线很短
            candle_info['body_ratio'] <= 0.3,  # 实体较小
            self._is_uptrend(df, index, 2)  # 前期上涨趋势
        ]
        
        if all(conditions):
            confidence = self._calculate_confidence(candle_info, 'shooting_star')
            return CandlestickPattern(
                pattern_type=PatternType.SHOOTING_STAR,
                signal_strength=SignalStrength.MODERATE,
                bullish=False,
                timestamp=current.name,
                price=current['close'],
                confidence=confidence,
                description="射击之星 - 潜在顶部反转信号"
            )
        return None
    
    def detect_doji(self, df: pd.DataFrame, index: int) -> Optional[CandlestickPattern]:
        """检测十字星形态"""
        current = df.iloc[index]
        candle_info = self.analyze_candle(current['open'], current['high'], 
                                        current['low'], current['close'])
        
        if candle_info['is_doji']:
            # 判断十字星类型和强度
            if candle_info['upper_shadow_ratio'] > 0.4 and candle_info['lower_shadow_ratio'] > 0.4:
                signal_strength = SignalStrength.STRONG
                description = "长腿十字星 - 强烈的不确定性信号"
            else:
                signal_strength = SignalStrength.MODERATE
                description = "十字星 - 市场犹豫不决"
            
            confidence = self._calculate_confidence(candle_info, 'doji')
            return CandlestickPattern(
                pattern_type=PatternType.DOJI,
                signal_strength=signal_strength,
                bullish=None,  # 十字星本身不表示方向
                timestamp=current.name,
                price=current['close'],
                confidence=confidence,
                description=description
            )
        return None
    
    def detect_engulfing_pattern(self, df: pd.DataFrame, index: int) -> Optional[CandlestickPattern]:
        """检测吞没形态"""
        if index < 1:
            return None
            
        prev_candle = df.iloc[index - 1]
        current_candle = df.iloc[index]
        
        prev_info = self.analyze_candle(prev_candle['open'], prev_candle['high'],
                                      prev_candle['low'], prev_candle['close'])
        current_info = self.analyze_candle(current_candle['open'], current_candle['high'],
                                         current_candle['low'], current_candle['close'])
        
        # 看涨吞没
        if (prev_info['is_bearish'] and current_info['is_bullish'] and
            current_candle['open'] < prev_candle['close'] and
            current_candle['close'] > prev_candle['open']):
            
            confidence = self._calculate_engulfing_confidence(prev_info, current_info, True)
            return CandlestickPattern(
                pattern_type=PatternType.BULLISH_ENGULFING,
                signal_strength=SignalStrength.STRONG,
                bullish=True,
                timestamp=current_candle.name,
                price=current_candle['close'],
                confidence=confidence,
                description="看涨吞没 - 强烈的底部反转信号"
            )
        
        # 看跌吞没
        elif (prev_info['is_bullish'] and current_info['is_bearish'] and
              current_candle['open'] > prev_candle['close'] and
              current_candle['close'] < prev_candle['open']):
            
            confidence = self._calculate_engulfing_confidence(prev_info, current_info, False)
            return CandlestickPattern(
                pattern_type=PatternType.BEARISH_ENGULFING,
                signal_strength=SignalStrength.STRONG,
                bullish=False,
                timestamp=current_candle.name,
                price=current_candle['close'],
                confidence=confidence,
                description="看跌吞没 - 强烈的顶部反转信号"
            )
        
        return None
    
    def detect_star_patterns(self, df: pd.DataFrame, index: int) -> Optional[CandlestickPattern]:
        """检测启明星/黄昏星形态"""
        if index < 2:
            return None
            
        first = df.iloc[index - 2]
        star = df.iloc[index - 1]
        third = df.iloc[index]
        
        first_info = self.analyze_candle(first['open'], first['high'], first['low'], first['close'])
        star_info = self.analyze_candle(star['open'], star['high'], star['low'], star['close'])
        third_info = self.analyze_candle(third['open'], third['high'], third['low'], third['close'])
        
        # 启明星形态
        if (first_info['is_bearish'] and star_info['body_ratio'] < 0.3 and third_info['is_bullish'] and
            star['high'] < min(first['close'], third['open']) and
            third['close'] > (first['open'] + first['close']) / 2):
            
            confidence = self._calculate_star_confidence(first_info, star_info, third_info, True)
            return CandlestickPattern(
                pattern_type=PatternType.MORNING_STAR,
                signal_strength=SignalStrength.STRONG,
                bullish=True,
                timestamp=third.name,
                price=third['close'],
                confidence=confidence,
                description="启明星 - 强烈的底部反转信号"
            )
        
        # 黄昏星形态
        elif (first_info['is_bullish'] and star_info['body_ratio'] < 0.3 and third_info['is_bearish'] and
              star['low'] > max(first['close'], third['open']) and
              third['close'] < (first['open'] + first['close']) / 2):
            
            confidence = self._calculate_star_confidence(first_info, star_info, third_info, False)
            return CandlestickPattern(
                pattern_type=PatternType.EVENING_STAR,
                signal_strength=SignalStrength.STRONG,
                bullish=False,
                timestamp=third.name,
                price=third['close'],
                confidence=confidence,
                description="黄昏星 - 强烈的顶部反转信号"
            )
        
        return None
    
    def scan_patterns(self, df: pd.DataFrame) -> List[CandlestickPattern]:
        """扫描所有蜡烛图形态"""
        patterns = []
        
        for i in range(len(df)):
            # 检测各种形态
            pattern_detectors = [
                self.detect_hammer,
                self.detect_shooting_star,
                self.detect_doji,
                self.detect_engulfing_pattern,
                self.detect_star_patterns
            ]
            
            for detector in pattern_detectors:
                pattern = detector(df, i)
                if pattern:
                    patterns.append(pattern)
        
        return patterns
    
    def _is_uptrend(self, df: pd.DataFrame, index: int, periods: int) -> bool:
        """判断是否处于上涨趋势"""
        if index < periods:
            return False
        
        recent_closes = df['close'].iloc[index-periods:index].values
        return np.all(np.diff(recent_closes) > 0)
    
    def _is_downtrend(self, df: pd.DataFrame, index: int, periods: int) -> bool:
        """判断是否处于下跌趋势"""
        if index < periods:
            return False
        
        recent_closes = df['close'].iloc[index-periods:index].values
        return np.all(np.diff(recent_closes) < 0)
    
    def _calculate_confidence(self, candle_info: Dict, pattern_type: str) -> float:
        """计算形态置信度"""
        base_confidence = 0.6
        
        # 根据影线比例调整置信度
        if pattern_type == 'hammer':
            confidence = base_confidence + (candle_info['lower_shadow_ratio'] - 0.6) * 0.5
        elif pattern_type == 'shooting_star':
            confidence = base_confidence + (candle_info['upper_shadow_ratio'] - 0.6) * 0.5
        elif pattern_type == 'doji':
            confidence = base_confidence + (1 - candle_info['body_ratio']) * 0.3
        else:
            confidence = base_confidence
        
        return min(max(confidence, 0.1), 0.95)
    
    def _calculate_engulfing_confidence(self, prev_info: Dict, current_info: Dict, bullish: bool) -> float:
        """计算吞没形态置信度"""
        base_confidence = 0.7
        
        # 实体大小比例影响置信度
        size_ratio = current_info['body'] / prev_info['body'] if prev_info['body'] > 0 else 1
        confidence_boost = min((size_ratio - 1) * 0.2, 0.2)
        
        return min(base_confidence + confidence_boost, 0.95)
    
    def _calculate_star_confidence(self, first_info: Dict, star_info: Dict, 
                                 third_info: Dict, bullish: bool) -> float:
        """计算星形形态置信度"""
        base_confidence = 0.8
        
        # 星的实体越小，置信度越高
        star_factor = (1 - star_info['body_ratio']) * 0.15
        
        return min(base_confidence + star_factor, 0.95)


# 使用示例
if __name__ == "__main__":
    # 示例数据格式
    sample_data = {
        'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H'),
        'open': np.random.randn(100).cumsum() + 50000,
        'high': np.random.randn(100).cumsum() + 50100,
        'low': np.random.randn(100).cumsum() + 49900,
        'close': np.random.randn(100).cumsum() + 50000,
        'volume': np.random.randint(1000, 10000, 100)
    }
    
    df = pd.DataFrame(sample_data)
    df.set_index('timestamp', inplace=True)
    
    # 创建检测器并扫描形态
    detector = CandlestickPatternDetector()
    patterns = detector.scan_patterns(df)
    
    # 输出检测到的形态
    for pattern in patterns:
        print(f"{pattern.timestamp}: {pattern.description} (置信度: {pattern.confidence:.2f})")
