@echo off
echo 正在将图像转换为视频...
echo 请确保已安装FFmpeg并添加到系统PATH中

REM 检查FFmpeg是否可用
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到FFmpeg
    echo 请下载并安装FFmpeg: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM 转换图像为视频 (每张图像显示3秒)
ffmpeg -framerate 1/3 -pattern_type glob -i "word_images/*.png" -c:v libx264 -r 30 -pix_fmt yuv420p word_video_from_images.mp4

if %errorlevel% equ 0 (
    echo 成功! 视频已保存为: word_video_from_images.mp4
) else (
    echo 转换失败，请检查错误信息
)

pause
