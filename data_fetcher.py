"""
加密货币数据获取器 - 仅支持OKX交易所
使用OKX官方SDK获取历史K线数据
"""

import pandas as pd
import time
from datetime import datetime, timedelta
import warnings

# 尝试导入OKX SDK
try:
    import okx.MarketData as MarketData
    import okx.PublicData as PublicData
    OKX_AVAILABLE = True
    print("✅ OKX SDK导入成功")
except ImportError:
    OKX_AVAILABLE = False
    print("❌ OKX SDK未安装，请运行: pip install python-okx")


class CryptoDataFetcher:
    """加密货币数据获取器 - 仅支持OKX交易所"""
    
    def __init__(self):
        """初始化数据获取器"""
        self.okx_market = None
        self.okx_public = None
        
        # 初始化OKX SDK
        self._init_okx_sdk()
        
        # 时间框架映射
        self.timeframe_mapping = {
            '1m': '1m',
            '5m': '5m', 
            '15m': '15m',
            '30m': '30m',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D',
            '1w': '1W'
        }
        
        # 交易对映射 (通用格式 -> OKX格式)
        self.symbol_mapping = {
            'BTC/USDT': 'BTC-USDT',
            'ETH/USDT': 'ETH-USDT',
            'BTC/ETH': 'BTC-ETH',
            'ADA/USDT': 'ADA-USDT',
            'SOL/USDT': 'SOL-USDT',
            'XRP/USDT': 'XRP-USDT',
            'DOT/USDT': 'DOT-USDT',
            'LINK/USDT': 'LINK-USDT',
            'LTC/USDT': 'LTC-USDT',
            'BCH/USDT': 'BCH-USDT'
        }
    
    def _init_okx_sdk(self):
        """初始化OKX SDK"""
        if not OKX_AVAILABLE:
            print("⚠️ OKX SDK不可用，无法初始化")
            return
        
        print("🚀 初始化OKX SDK...")
        
        # OKX配置
        okx_config = {
            'api_key': '',           # 留空，仅获取公开数据
            'api_secret_key': '',    # 留空，仅获取公开数据
            'passphrase': '',        # 留空，仅获取公开数据
            'flag': '1',             # 1=模拟交易，0=实盘交易
            'domain': 'https://www.okx.com'
        }
        
        try:
            # 初始化市场数据API
            self.okx_market = MarketData.MarketAPI(**okx_config)
            
            # 初始化公共数据API
            self.okx_public = PublicData.PublicAPI(**okx_config)
            
            print("✅ OKX SDK初始化成功")
            
            # 测试连接
            self._test_okx_connection()
            
        except Exception as e:
            print(f"❌ OKX SDK初始化失败: {e}")
            self.okx_market = None
            self.okx_public = None
    
    def _test_okx_connection(self):
        """测试OKX API连接"""
        if not self.okx_market:
            return
        
        try:
            # 获取BTC-USDT的ticker信息
            result = self.okx_market.get_tickers(instType='SPOT')
            
            if result and 'code' in result and result['code'] == '0':
                print("✅ OKX API连接测试成功")
            else:
                print(f"⚠️ OKX API连接测试失败: {result}")
                
        except Exception as e:
            print(f"❌ OKX API连接测试异常: {e}")
    
    def _convert_symbol(self, symbol):
        """转换交易对格式"""
        return self.symbol_mapping.get(symbol, symbol.replace('/', '-'))
    
    def _convert_timeframe(self, timeframe):
        """转换时间框架格式"""
        return self.timeframe_mapping.get(timeframe, timeframe)
    
    def _parse_datetime(self, date_str):
        """解析日期字符串为时间戳"""
        try:
            dt = pd.to_datetime(date_str)
            return int(dt.timestamp() * 1000)  # 转换为毫秒时间戳
        except:
            return None
    
    def fetch_okx_data(self, symbol, timeframe, start_date, end_date, limit=300):
        """
        从OKX获取K线数据
        
        Args:
            symbol (str): 交易对，如 'BTC/USDT'
            timeframe (str): 时间框架，如 '1h'
            start_date (str): 开始日期，如 '2024-01-01'
            end_date (str): 结束日期，如 '2024-01-02'
            limit (int): 数据条数限制，默认300
            
        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        if not self.okx_market:
            print("❌ OKX SDK未初始化")
            return pd.DataFrame()
        
        # 转换格式
        okx_symbol = self._convert_symbol(symbol)
        interval = self._convert_timeframe(timeframe)
        
        print(f"📊 正在从OKX获取 {symbol} 数据 ({start_date} 至 {end_date})...")
        print(f"🔗 交易对: {okx_symbol}, 时间框架: {interval}")
        
        try:
            # 调用OKX API获取K线数据
            result = self.okx_market.get_candlesticks(
                instId=okx_symbol,
                bar=interval,
                limit=limit
            )
            
            # 检查API响应
            if result and 'code' in result and result['code'] == '0':
                if 'data' in result and result['data']:
                    candles = result['data']
                    print(f"✅ 成功获取 {len(candles)} 条K线数据")
                    
                    # 创建DataFrame
                    df = pd.DataFrame(candles, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 
                        'volume', 'currency_volume', 'extra1', 'extra2'
                    ])
                    
                    # 只保留需要的列
                    df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                    
                    # 数据类型转换
                    df['timestamp'] = pd.to_datetime(df['timestamp'].astype(float), unit='ms')
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    # 设置索引
                    df.set_index('timestamp', inplace=True)
                    
                    # 调试信息
                    print(f"🔍 原始数据时间范围: {df.index.min()} 至 {df.index.max()}")
                    print(f"🔍 请求时间范围: {start_date} 至 {end_date}")
                    
                    # 过滤时间范围
                    if start_date and end_date:
                        start_dt = pd.to_datetime(start_date)
                        end_dt = pd.to_datetime(end_date)
                        print(f"🔍 转换后时间范围: {start_dt} 至 {end_dt}")
                        
                        # 检查是否有重叠
                        if df.index.max() < start_dt or df.index.min() > end_dt:
                            print("⚠️ 数据时间范围与请求范围不重叠，返回所有数据")
                            print(f"📈 处理后数据: {len(df)} 条记录")
                            print(f"⏰ 时间范围: {df.index.min()} 至 {df.index.max()}")
                            return df
                        
                        df = df[(df.index >= start_dt) & (df.index <= end_dt)]
                    
                    print(f"📈 处理后数据: {len(df)} 条记录")
                    print(f"⏰ 时间范围: {df.index.min()} 至 {df.index.max()}")
                    
                    return df
                else:
                    print("⚠️ OKX API返回数据为空")
                    return pd.DataFrame()
            else:
                print(f"❌ OKX API调用失败: {result}")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ OKX数据获取异常: {e}")
            return pd.DataFrame()
    
    def fetch_data(self, symbol, timeframe, start_date, end_date, source='okx'):
        """
        获取加密货币数据
        
        Args:
            symbol (str): 交易对
            timeframe (str): 时间框架
            start_date (str): 开始日期
            end_date (str): 结束日期
            source (str): 数据源，目前只支持'okx'
            
        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        if source.lower() != 'okx':
            print(f"⚠️ 目前只支持OKX数据源，自动切换到OKX")
            source = 'okx'
        
        return self.fetch_okx_data(symbol, timeframe, start_date, end_date)
    
    def get_available_symbols(self):
        """获取可用的交易对列表"""
        if not self.okx_public:
            return []
        
        try:
            result = self.okx_public.get_instruments(instType='SPOT')
            
            if result and 'code' in result and result['code'] == '0':
                symbols = [item['instId'] for item in result.get('data', [])]
                return symbols[:20]  # 只返回前20个
            else:
                return []
                
        except Exception as e:
            print(f"❌ 获取交易对列表失败: {e}")
            return []
    
    def get_market_status(self):
        """获取市场状态"""
        if not self.okx_market:
            return "未连接"
        
        try:
            result = self.okx_market.get_tickers(instType='SPOT')
            
            if result and 'code' in result and result['code'] == '0':
                return "正常"
            else:
                return "异常"
                
        except Exception:
            return "异常"


# 使用示例
if __name__ == "__main__":
    print("🚀 加密货币数据获取器测试")
    print("=" * 50)
    
    # 创建数据获取器
    fetcher = CryptoDataFetcher()
    
    # 测试获取数据
    if fetcher.okx_market:
        print("\n📊 测试数据获取...")
        
        # 获取BTC/USDT的1小时数据
        data = fetcher.fetch_data(
            symbol='BTC/USDT',
            timeframe='1h',
            start_date='2024-01-01',
            end_date='2024-01-02',
            source='okx'
        )
        
        if not data.empty:
            print(f"\n✅ 成功获取数据:")
            print(f"   数据条数: {len(data)}")
            print(f"   时间范围: {data.index.min()} 至 {data.index.max()}")
            print(f"   价格范围: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            print(f"   最新价格: ${data['close'].iloc[-1]:.2f}")
        else:
            print("❌ 数据获取失败")
    else:
        print("❌ OKX SDK未初始化，无法测试")
    
    print("\n" + "=" * 50)
    print("测试完成")
