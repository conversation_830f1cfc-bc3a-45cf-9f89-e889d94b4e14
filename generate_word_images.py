from PIL import Image, ImageDraw, ImageFont
import os

def create_word_image(word, pronunciation, meaning, width=1920, height=1080, font_size=70, output_path="word_image.png"):
    """创建单个单词的图像"""
    # 创建黑色背景
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        try:
            # Windows系统字体路径
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/tahoma.ttf"
            ]
            
            font_large = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font_large = ImageFont.truetype(font_path, font_size)
                    font_medium = ImageFont.truetype(font_path, int(font_size * 0.7))
                    font_small = ImageFont.truetype(font_path, int(font_size * 0.6))
                    break
            
            if font_large is None:
                raise Exception("No system fonts found")
                
        except Exception as e:
            print(f"使用系统字体失败: {e}")
            # 使用默认字体
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 计算文本位置 (居中)
        # 单词 (大字体，白色)
        word_bbox = draw.textbbox((0, 0), word, font=font_large)
        word_width = word_bbox[2] - word_bbox[0]
        word_x = (width - word_width) // 2
        word_y = 250
        
        # 音标 (中等字体，黄色)
        pronunciation_bbox = draw.textbbox((0, 0), pronunciation, font=font_medium)
        pronunciation_width = pronunciation_bbox[2] - pronunciation_bbox[0]
        pronunciation_x = (width - pronunciation_width) // 2
        pronunciation_y = 400
        
        # 释义 (小字体，浅蓝色)
        meaning_bbox = draw.textbbox((0, 0), meaning, font=font_small)
        meaning_width = meaning_bbox[2] - meaning_bbox[0]
        meaning_x = (width - meaning_width) // 2
        meaning_y = 550
        
        # 绘制文本
        draw.text((word_x, word_y), word, fill='white', font=font_large)
        draw.text((pronunciation_x, pronunciation_y), pronunciation, fill='yellow', font=font_medium)
        draw.text((meaning_x, meaning_y), meaning, fill='lightblue', font=font_small)
        
    except Exception as e:
        print(f"绘制文本时出错: {e}")
        # 使用简单的文本绘制作为备用
        draw.text((100, 300), word, fill='white')
        draw.text((100, 400), pronunciation, fill='yellow')
        draw.text((100, 500), meaning, fill='lightblue')
    
    # 保存图像
    img.save(output_path)
    return output_path

def generate_word_images(input_file, output_dir="word_images", font_size=70):
    """为每个单词生成图像"""
    print("开始生成单词图像...")
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 读取单词文件
    try:
        with open(input_file, "r", encoding="utf-8") as file:
            lines = [line.strip() for line in file.readlines() if line.strip()][:10]
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    if not lines:
        print("错误: 文件中没有有效的单词数据")
        return
    
    print(f"处理 {len(lines)} 个单词")
    
    generated_images = []
    
    for i, line in enumerate(lines, 1):
        print(f"处理第 {i} 个单词: {line[:50]}...")
        
        # 解析单词行
        parts = line.split(" ", 2)
        if len(parts) < 3:
            print(f"  跳过格式不正确的行: {line}")
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]
        
        # 生成图像文件名
        output_path = os.path.join(output_dir, f"word_{i:02d}_{word}.png")
        
        try:
            create_word_image(word, pronunciation, meaning, 1920, 1080, font_size, output_path)
            generated_images.append(output_path)
            print(f"  ✓ 图像已保存: {output_path}")
        except Exception as e:
            print(f"  ✗ 图像生成失败: {e}")
            continue
    
    print(f"\\n✓ 成功生成 {len(generated_images)} 个图像")
    print(f"图像保存在目录: {output_dir}")
    
    # 生成转换为视频的说明
    print("\\n" + "="*60)
    print("如何将图像转换为视频:")
    print("="*60)
    print("方法1: 使用FFmpeg (推荐)")
    print("1. 下载并安装FFmpeg: https://ffmpeg.org/download.html")
    print("2. 在命令行中运行以下命令:")
    print(f"   ffmpeg -framerate 1/3 -i {output_dir}/word_%02d_*.png -c:v libx264 -r 30 -pix_fmt yuv420p word_video.mp4")
    print("   (每个图像显示3秒)")
    print()
    print("方法2: 使用在线工具")
    print("1. 访问 https://www.online-convert.com/ 或类似网站")
    print("2. 选择 'Convert to MP4'")
    print("3. 上传所有图像文件")
    print("4. 设置帧率和其他参数")
    print()
    print("方法3: 使用视频编辑软件")
    print("1. 使用Adobe Premiere、DaVinci Resolve等软件")
    print("2. 导入所有图像")
    print("3. 设置每张图像的显示时间为3秒")
    print("4. 导出为MP4格式")
    
    return generated_images

def create_batch_file():
    """创建批处理文件来自动转换视频"""
    batch_content = '''@echo off
echo 正在将图像转换为视频...
echo 请确保已安装FFmpeg并添加到系统PATH中

REM 检查FFmpeg是否可用
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到FFmpeg
    echo 请下载并安装FFmpeg: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM 转换图像为视频 (每张图像显示3秒)
ffmpeg -framerate 1/3 -pattern_type glob -i "word_images/*.png" -c:v libx264 -r 30 -pix_fmt yuv420p word_video_from_images.mp4

if %errorlevel% equ 0 (
    echo 成功! 视频已保存为: word_video_from_images.mp4
) else (
    echo 转换失败，请检查错误信息
)

pause
'''
    
    with open("convert_to_video.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("\\n✓ 已创建批处理文件: convert_to_video.bat")
    print("双击运行此文件可自动转换图像为视频 (需要安装FFmpeg)")

def main():
    """主函数"""
    input_file = "Random_10_Words.txt"
    output_dir = "word_images"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return
    
    # 生成图像
    generated_images = generate_word_images(
        input_file=input_file,
        output_dir=output_dir,
        font_size=70
    )
    
    if generated_images:
        # 创建批处理文件
        create_batch_file()
        
        print(f"\\n🎉 完成! 生成了 {len(generated_images)} 个单词图像")
        print("\\n下一步:")
        print("1. 如果您有FFmpeg，可以运行 convert_to_video.bat")
        print("2. 或者使用上面提到的其他方法转换为视频")

if __name__ == "__main__":
    main()
