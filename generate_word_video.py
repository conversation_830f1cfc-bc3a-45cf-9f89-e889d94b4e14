from moviepy.editor import TextClip, CompositeVideoClip, concatenate_videoclips
from moviepy.video.fx.fadein import fadein
from moviepy.video.fx.fadeout import fadeout


from moviepy.config import change_settings
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16\\magick.exe"})

def generate_word_video(input_file, output_file="output.mp4", font_size=70, total_duration=30):
    """
    从TXT文件生成单词视频（10个单词，总时长30秒）
    :param input_file: 输入的TXT文件路径
    :param output_file: 输出的视频文件路径
    :param font_size: 字体大小
    :param total_duration: 视频总时长（秒）
    """
    # 读取TXT文件（仅前10行）
    with open(input_file, "r", encoding="utf-8") as file:
        lines = [line.strip() for line in file.readlines() if line.strip()][:10]

    clips = []
    duration_per_word = total_duration / len(lines)  # 每个单词平均时长

    print(f"处理 {len(lines)} 个单词，每个单词时长: {duration_per_word:.2f} 秒")

    for i, line in enumerate(lines):
        print(f"处理第 {i+1} 个单词: {line[:20]}...")

        # 解析单词、音标和释义
        parts = line.split(" ", 2)
        if len(parts) < 3:
            print(f"跳过格式不正确的行: {line}")
            continue

        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]

        # 生成单词片段（白色文字，深色背景）
        word_duration = max(1.0, duration_per_word * 0.4)  # 确保最小1秒
        print(f"  单词片段时长: {word_duration:.2f} 秒")

        try:
            word_clip = TextClip(
                word,
                fontsize=font_size,
                color="white",
                bg_color="#121212",
                size=(1920, 1080),
            ).set_duration(word_duration).set_fps(24)

            # 添加渐入渐出效果
            fade_duration = min(0.3, word_duration/4)
            word_clip = word_clip.fx(fadein, fade_duration).fx(fadeout, fade_duration)

            print(f"  单词片段创建成功")
        except Exception as e:
            print(f"  单词片段创建失败: {e}")
            continue

        # 生成音标和释义片段（黄色文字）
        info_duration = max(1.5, duration_per_word * 0.6)  # 确保最小1.5秒
        print(f"  信息片段时长: {info_duration:.2f} 秒")

        try:
            info_clip = TextClip(
                f"{pronunciation}\n{meaning}",
                fontsize=int(font_size * 0.8),
                color="yellow",
                bg_color="#121212",
                size=(1920, 1080),
            ).set_duration(info_duration).set_fps(24)

            # 添加渐入渐出效果
            fade_duration = min(0.3, info_duration/4)
            info_clip = info_clip.fx(fadein, fade_duration).fx(fadeout, fade_duration)

            print(f"  信息片段创建成功")
        except Exception as e:
            print(f"  信息片段创建失败: {e}")
            continue

        # 合并片段
        try:
            combined_clip = concatenate_videoclips([word_clip, info_clip])
            clips.append(combined_clip)
            print(f"  片段合并成功，总时长: {combined_clip.duration:.2f} 秒")
        except Exception as e:
            print(f"  片段合并失败: {e}")
            continue

    if not clips:
        print("错误: 没有成功创建任何视频片段")
        return

    print(f"开始合并 {len(clips)} 个片段...")

    # 合并所有单词片段
    try:
        final_clip = concatenate_videoclips(clips)
        print(f"最终视频时长: {final_clip.duration:.2f} 秒")

        print("开始写入视频文件...")
        # 设置fps并写入视频
        final_clip = final_clip.set_fps(24)
        final_clip.write_videofile(output_file, codec="libx264", verbose=True)
        print(f"视频已保存为: {output_file}")

    except Exception as e:
        print(f"视频合并或写入失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    input_file = "Random_10_Words.txt"  # 替换为你的TXT文件路径
    output_file = "word_video.mp4"
    generate_word_video(input_file, output_file)