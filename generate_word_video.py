from moviepy.editor import TextClip, CompositeVideoClip, concatenate_videoclips
from moviepy.video.fx import fadein, fadeout, resize
import os
from moviepy.config import change_settings
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16-HDRI\\magick.exe"})

def generate_word_video(input_file, output_file="output.mp4", font_size=70, total_duration=30):
    """
    从TXT文件生成单词视频（10个单词，总时长30秒）
    :param input_file: 输入的TXT文件路径
    :param output_file: 输出的视频文件路径
    :param font_size: 字体大小
    :param total_duration: 视频总时长（秒）
    """
    # 读取TXT文件（仅前10行）
    with open(input_file, "r", encoding="utf-8") as file:
        lines = [line.strip() for line in file.readlines() if line.strip()][:10]

    clips = []
    duration_per_word = total_duration / len(lines)  # 每个单词平均时长

    for line in lines:
        # 解析单词、音标和释义
        parts = line.split(" ", 2)
        if len(parts) < 3:
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]

        # 生成单词片段（白色文字，深色背景）
        word_clip = (
            TextClip(
                word,
                fontsize=font_size,
                color="white",
                bg_color="#121212",  # 深灰色背景
                size=(1920, 1080),
            )
            .set_duration(duration_per_word * 0.4)  # 单词显示40%时长
            .fx(fadein, 0.5)  # 渐入效果
            .fx(fadeout, 0.5)  # 渐出效果
            .fx(resize, lambda t: 1 + 0.1 * t)  # 轻微缩放动画
        )

        # 生成音标和释义片段（黄色文字）
        info_clip = (
            TextClip(
                f"{pronunciation}\n{meaning}",
                fontsize=int(font_size * 0.8),
                color="yellow",
                bg_color="#121212",
                size=(1920, 1080),
            )
            .set_duration(duration_per_word * 0.6)  # 音标/释义显示60%时长
            .fx(fadein, 0.5)
            .fx(fadeout, 0.5)
        )

        # 合并片段
        combined_clip = concatenate_videoclips([word_clip, info_clip])
        clips.append(combined_clip)

    # 合并所有单词片段
    final_clip = concatenate_videoclips(clips)
    final_clip.write_videofile(output_file, fps=24, codec="libx264")

if __name__ == "__main__":
    input_file = "Random_10_Words.txt"  # 替换为你的TXT文件路径
    output_file = "word_video.mp4"
    generate_word_video(input_file, output_file)