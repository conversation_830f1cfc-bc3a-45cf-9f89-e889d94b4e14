from moviepy.editor import TextClip, concatenate_videoclips, ColorClip, CompositeVideoClip
from moviepy.config import change_settings
import os

# 设置ImageMagick路径
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16\\magick.exe"})

def create_word_clip(word, pronunciation, meaning, duration, font_size=70):
    """创建单个单词的视频片段"""
    try:
        # 创建黑色背景
        background = ColorClip(size=(1920, 1080), color=(0, 0, 0))
        
        # 创建单词文本 (大字体，白色)
        word_clip = TextClip(
            word,
            fontsize=font_size,
            color='white',
            font='Arial-Bold'
        ).set_position(('center', 300)).set_duration(duration)
        
        # 创建音标文本 (中等字体，黄色)
        pronunciation_clip = TextClip(
            pronunciation,
            fontsize=int(font_size * 0.7),
            color='yellow',
            font='Arial'
        ).set_position(('center', 450)).set_duration(duration)
        
        # 创建释义文本 (小字体，浅蓝色)
        meaning_clip = TextClip(
            meaning,
            fontsize=int(font_size * 0.6),
            color='lightblue',
            font='Arial'
        ).set_position(('center', 600)).set_duration(duration)
        
        # 合成视频片段
        video_clip = CompositeVideoClip([
            background.set_duration(duration),
            word_clip,
            pronunciation_clip,
            meaning_clip
        ], size=(1920, 1080))
        
        return video_clip
        
    except Exception as e:
        print(f"创建片段时出错: {e}")
        # 创建简单的文本片段作为备用
        simple_text = f"{word}\\n{pronunciation}\\n{meaning}"
        simple_clip = TextClip(
            simple_text,
            fontsize=40,
            color='white',
            font='Arial'
        ).set_position('center').set_duration(duration)
        
        background = ColorClip(size=(1920, 1080), color=(0, 0, 0)).set_duration(duration)
        return CompositeVideoClip([background, simple_clip], size=(1920, 1080))

def generate_word_video_final(input_file, output_file="word_video_final.mp4", font_size=70, total_duration=30):
    """
    生成单词学习视频的最终版本
    """
    print("开始生成单词学习视频...")
    
    # 读取单词文件
    try:
        with open(input_file, "r", encoding="utf-8") as file:
            lines = [line.strip() for line in file.readlines() if line.strip()][:10]
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    if not lines:
        print("错误: 文件中没有有效的单词数据")
        return
    
    clips = []
    duration_per_word = total_duration / len(lines)
    
    print(f"处理 {len(lines)} 个单词，每个单词显示 {duration_per_word:.2f} 秒")
    
    for i, line in enumerate(lines, 1):
        print(f"处理第 {i} 个单词: {line[:50]}...")
        
        # 解析单词行
        parts = line.split(" ", 2)
        if len(parts) < 3:
            print(f"  跳过格式不正确的行: {line}")
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]
        
        # 创建视频片段
        try:
            clip = create_word_clip(word, pronunciation, meaning, duration_per_word, font_size)
            clips.append(clip)
            print(f"  ✓ 片段创建成功")
        except Exception as e:
            print(f"  ✗ 片段创建失败: {e}")
            continue
    
    if not clips:
        print("错误: 没有成功创建任何视频片段")
        return
    
    print(f"\\n开始合并 {len(clips)} 个视频片段...")
    
    try:
        # 合并所有片段
        final_video = concatenate_videoclips(clips, method="compose")
        print(f"合并完成，总时长: {final_video.duration:.2f} 秒")
        
        # 写入视频文件
        print(f"正在保存视频到: {output_file}")
        print("这可能需要几分钟时间，请耐心等待...")
        
        # 使用最基本的参数来避免fps问题
        final_video.write_videofile(
            output_file,
            codec='libx264',
            audio_codec='aac' if final_video.audio else None,
            temp_audiofile='temp-audio.m4a' if final_video.audio else None,
            remove_temp=True
        )
        
        print(f"\\n✓ 视频生成成功！")
        print(f"输出文件: {output_file}")
        print(f"视频时长: {final_video.duration:.2f} 秒")
        
        # 检查文件是否存在
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
        
    except Exception as e:
        print(f"\\n✗ 视频生成失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 提供一些故障排除建议
        print("\\n故障排除建议:")
        print("1. 确保ImageMagick已正确安装")
        print("2. 确保有足够的磁盘空间")
        print("3. 尝试使用较小的字体大小或较短的总时长")

def main():
    """主函数"""
    input_file = "Random_10_Words.txt"
    output_file = "word_video_final.mp4"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        print("请确保文件存在并包含单词数据")
        return
    
    # 生成视频
    generate_word_video_final(
        input_file=input_file,
        output_file=output_file,
        font_size=70,
        total_duration=30
    )

if __name__ == "__main__":
    main()
