from moviepy.editor import TextClip, concatenate_videoclips, ColorClip, CompositeVideoClip
from moviepy.config import change_settings
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16\\magick.exe"})

def generate_word_video_fixed(input_file, output_file="output.mp4", font_size=70, total_duration=30):
    """
    从TXT文件生成单词视频（修复版本）
    """
    # 读取TXT文件
    with open(input_file, "r", encoding="utf-8") as file:
        lines = [line.strip() for line in file.readlines() if line.strip()][:10]

    clips = []
    duration_per_word = total_duration / len(lines)
    
    print(f"处理 {len(lines)} 个单词，每个单词时长: {duration_per_word:.2f} 秒")

    for i, line in enumerate(lines):
        print(f"处理第 {i+1} 个单词: {line[:30]}...")
        
        # 解析单词、音标和释义
        parts = line.split(" ", 2)
        if len(parts) < 3:
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]

        try:
            # 创建背景
            background = ColorClip(size=(1920, 1080), color=(18, 18, 18)).set_duration(duration_per_word)
            
            # 创建单词文本
            word_text = TextClip(
                word,
                fontsize=font_size,
                color="white",
                font="Arial-Bold"
            ).set_position('center').set_duration(duration_per_word)
            
            # 创建音标文本
            pronunciation_text = TextClip(
                pronunciation,
                fontsize=int(font_size * 0.7),
                color="yellow",
                font="Arial"
            ).set_position(('center', 'center')).set_duration(duration_per_word)
            
            # 创建释义文本
            meaning_text = TextClip(
                meaning,
                fontsize=int(font_size * 0.6),
                color="lightblue",
                font="Arial"
            ).set_position(('center', 'bottom')).set_duration(duration_per_word)
            
            # 调整文本位置
            word_text = word_text.set_position(('center', 200))
            pronunciation_text = pronunciation_text.set_position(('center', 400))
            meaning_text = meaning_text.set_position(('center', 600))
            
            # 合成视频
            video = CompositeVideoClip([background, word_text, pronunciation_text, meaning_text])
            clips.append(video)
            
            print(f"  片段创建成功")
            
        except Exception as e:
            print(f"  片段创建失败: {e}")
            # 创建一个简单的文本片段作为备用
            try:
                simple_text = f"{word}\\n{pronunciation}\\n{meaning}"
                simple_clip = TextClip(
                    simple_text,
                    fontsize=50,
                    color="white"
                ).set_duration(duration_per_word).set_position('center')
                
                background = ColorClip(size=(1920, 1080), color=(0, 0, 0)).set_duration(duration_per_word)
                video = CompositeVideoClip([background, simple_clip])
                clips.append(video)
                print(f"  使用简单片段")
            except Exception as e2:
                print(f"  简单片段也失败: {e2}")
                continue

    if not clips:
        print("错误: 没有成功创建任何视频片段")
        return

    print(f"开始合并 {len(clips)} 个片段...")
    
    try:
        # 合并所有片段
        final_clip = concatenate_videoclips(clips)
        print(f"最终视频时长: {final_clip.duration:.2f} 秒")
        
        print("开始写入视频文件...")
        # 使用更简单的参数
        final_clip.write_videofile(
            output_file,
            fps=24,
            verbose=False
        )
        print(f"视频已保存为: {output_file}")
        
    except Exception as e:
        print(f"视频合并或写入失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    input_file = "Random_10_Words.txt"
    output_file = "word_video_fixed.mp4"
    generate_word_video_fixed(input_file, output_file)
