import imageio
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_word_frame(word, pronunciation, meaning, width=1920, height=1080, font_size=70):
    """创建单个单词的视频帧"""
    # 创建黑色背景
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf", 
            "C:/Windows/Fonts/tahoma.ttf"
        ]
        
        font_large = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                font_large = ImageFont.truetype(font_path, font_size)
                font_medium = ImageFont.truetype(font_path, int(font_size * 0.75))
                font_small = ImageFont.truetype(font_path, int(font_size * 0.6))
                break
        
        if font_large is None:
            # 使用默认字体
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 计算文本位置 (居中)
        # 单词 (大字体，白色)
        word_bbox = draw.textbbox((0, 0), word, font=font_large)
        word_width = word_bbox[2] - word_bbox[0]
        word_x = (width - word_width) // 2
        word_y = int(height * 0.25)
        
        # 音标 (中等字体，黄色)
        pronunciation_bbox = draw.textbbox((0, 0), pronunciation, font=font_medium)
        pronunciation_width = pronunciation_bbox[2] - pronunciation_bbox[0]
        pronunciation_x = (width - pronunciation_width) // 2
        pronunciation_y = int(height * 0.45)
        
        # 释义 (小字体，浅蓝色)
        meaning_bbox = draw.textbbox((0, 0), meaning, font=font_small)
        meaning_width = meaning_bbox[2] - meaning_bbox[0]
        meaning_x = (width - meaning_width) // 2
        meaning_y = int(height * 0.65)
        
        # 绘制文本
        draw.text((word_x, word_y), word, fill='white', font=font_large)
        draw.text((pronunciation_x, pronunciation_y), pronunciation, fill='yellow', font=font_medium)
        draw.text((meaning_x, meaning_y), meaning, fill='lightblue', font=font_small)
        
    except Exception as e:
        print(f"绘制文本时出错: {e}")
        # 使用简单的文本绘制
        draw.text((100, 300), word, fill='white')
        draw.text((100, 450), pronunciation, fill='yellow')
        draw.text((100, 600), meaning, fill='lightblue')
    
    # 转换为numpy数组
    return np.array(img)

def generate_word_video_imageio(input_file, output_file="word_video_imageio.mp4", 
                               total_duration=30, fps=24, font_size=70):
    """使用imageio生成单词学习视频"""
    
    print("🎬 开始使用imageio生成单词学习视频...")
    
    # 读取单词文件
    try:
        with open(input_file, "r", encoding="utf-8") as file:
            lines = [line.strip() for line in file.readlines() if line.strip()][:10]
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False
    
    if not lines:
        print("❌ 错误: 文件中没有有效的单词数据")
        return False
    
    # 计算参数
    duration_per_word = total_duration / len(lines)
    frames_per_word = int(duration_per_word * fps)
    total_frames = frames_per_word * len(lines)
    
    print(f"📝 处理 {len(lines)} 个单词")
    print(f"⏱️  每个单词显示 {duration_per_word:.2f} 秒 ({frames_per_word} 帧)")
    print(f"🎞️  总帧数: {total_frames}")
    print(f"📊 帧率: {fps} fps")
    
    # 创建视频写入器
    try:
        writer = imageio.get_writer(output_file, fps=fps, codec='libx264')
        print(f"✅ 视频写入器创建成功")
    except Exception as e:
        print(f"❌ 创建视频写入器失败: {e}")
        # 尝试使用默认参数
        try:
            writer = imageio.get_writer(output_file, fps=fps)
            print(f"✅ 使用默认参数创建视频写入器成功")
        except Exception as e2:
            print(f"❌ 创建视频写入器完全失败: {e2}")
            return False
    
    try:
        frame_count = 0
        
        for i, line in enumerate(lines, 1):
            print(f"🔄 处理第 {i} 个单词: {line[:50]}...")
            
            # 解析单词行
            parts = line.split(" ", 2)
            if len(parts) < 3:
                print(f"⚠️  跳过格式不正确的行: {line}")
                continue
            
            word = parts[0]
            pronunciation = parts[1]
            meaning = parts[2]
            
            try:
                # 创建帧
                frame = create_word_frame(word, pronunciation, meaning, 1920, 1080, font_size)
                
                # 写入多帧 (每个单词显示指定的帧数)
                for j in range(frames_per_word):
                    writer.append_data(frame)
                    frame_count += 1
                    
                    # 显示进度
                    if frame_count % (fps * 2) == 0:  # 每2秒显示一次进度
                        progress = (frame_count / total_frames) * 100
                        print(f"📈 进度: {progress:.1f}% ({frame_count}/{total_frames} 帧)")
                
                print(f"✅ 单词 {i} 处理完成 ({frames_per_word} 帧)")
                
            except Exception as e:
                print(f"❌ 处理单词 {i} 时出错: {e}")
                continue
        
        print(f"🔗 正在完成视频文件...")
        
    except Exception as e:
        print(f"❌ 生成视频时出错: {e}")
        return False
    
    finally:
        # 关闭写入器
        try:
            writer.close()
            print(f"✅ 视频写入器已关闭")
        except:
            pass
    
    # 检查输出文件
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        print(f"\\n🎉 视频生成成功！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 文件大小: {file_size:.2f} MB")
        print(f"⏱️  视频时长: {total_duration} 秒")
        print(f"🎞️  分辨率: 1920x1080")
        print(f"📈 帧率: {fps} fps")
        return True
    else:
        print(f"❌ 视频文件未生成")
        return False

def main():
    """主函数"""
    input_file = "Random_10_Words.txt"
    output_file = "word_video_imageio.mp4"
    
    print("=" * 60)
    print("🎯 单词学习视频生成器 (imageio版本)")
    print("=" * 60)
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件 {input_file} 不存在")
        return
    
    # 检查imageio
    try:
        print(f"📦 imageio 版本: {imageio.__version__}")
    except:
        print("❌ imageio 未安装，请运行: pip install imageio[ffmpeg]")
        return
    
    # 检查PIL
    try:
        from PIL import Image
        print(f"📦 PIL 可用")
    except:
        print("❌ PIL 未安装，请运行: pip install Pillow")
        return
    
    # 生成视频
    success = generate_word_video_imageio(
        input_file=input_file,
        output_file=output_file,
        total_duration=30,
        fps=24,
        font_size=70
    )
    
    if success:
        print("\\n🎊 任务完成！您的单词学习视频已生成。")
        print("\\n📺 您现在可以播放视频文件来查看效果。")
    else:
        print("\\n😞 视频生成失败，请检查错误信息。")
        print("\\n💡 建议:")
        print("1. 安装imageio的ffmpeg插件: pip install imageio[ffmpeg]")
        print("2. 确保有足够的磁盘空间")
        print("3. 检查系统权限")

if __name__ == "__main__":
    main()
