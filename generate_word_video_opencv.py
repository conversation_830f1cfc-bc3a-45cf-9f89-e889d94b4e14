import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_text_image(word, pronunciation, meaning, width=1920, height=1080, font_size=70):
    """使用PIL创建文本图像"""
    # 创建黑色背景
    img = Image.new('RGB', (width, height), color='black')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        try:
            font_large = ImageFont.truetype("arial.ttf", font_size)
            font_medium = ImageFont.truetype("arial.ttf", int(font_size * 0.7))
            font_small = ImageFont.truetype("arial.ttf", int(font_size * 0.6))
        except:
            # 如果找不到字体，使用默认字体
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # 计算文本位置
        word_bbox = draw.textbbox((0, 0), word, font=font_large)
        word_width = word_bbox[2] - word_bbox[0]
        word_x = (width - word_width) // 2
        word_y = 250
        
        pronunciation_bbox = draw.textbbox((0, 0), pronunciation, font=font_medium)
        pronunciation_width = pronunciation_bbox[2] - pronunciation_bbox[0]
        pronunciation_x = (width - pronunciation_width) // 2
        pronunciation_y = 400
        
        meaning_bbox = draw.textbbox((0, 0), meaning, font=font_small)
        meaning_width = meaning_bbox[2] - meaning_bbox[0]
        meaning_x = (width - meaning_width) // 2
        meaning_y = 550
        
        # 绘制文本
        draw.text((word_x, word_y), word, fill='white', font=font_large)
        draw.text((pronunciation_x, pronunciation_y), pronunciation, fill='yellow', font=font_medium)
        draw.text((meaning_x, meaning_y), meaning, fill='lightblue', font=font_small)
        
    except Exception as e:
        print(f"绘制文本时出错: {e}")
        # 使用简单的文本绘制
        draw.text((100, 300), f"{word}", fill='white')
        draw.text((100, 400), f"{pronunciation}", fill='yellow')
        draw.text((100, 500), f"{meaning}", fill='lightblue')
    
    # 转换为numpy数组
    return np.array(img)

def generate_word_video_opencv(input_file, output_file="word_video_opencv.mp4", font_size=70, total_duration=30, fps=24):
    """使用OpenCV生成单词学习视频"""
    print("开始使用OpenCV生成单词学习视频...")
    
    # 读取单词文件
    try:
        with open(input_file, "r", encoding="utf-8") as file:
            lines = [line.strip() for line in file.readlines() if line.strip()][:10]
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    if not lines:
        print("错误: 文件中没有有效的单词数据")
        return
    
    # 视频参数
    width, height = 1920, 1080
    duration_per_word = total_duration / len(lines)
    frames_per_word = int(duration_per_word * fps)
    
    print(f"处理 {len(lines)} 个单词")
    print(f"每个单词显示 {duration_per_word:.2f} 秒 ({frames_per_word} 帧)")
    print(f"总帧数: {frames_per_word * len(lines)}")
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))
    
    if not out.isOpened():
        print("错误: 无法创建视频文件")
        return
    
    try:
        for i, line in enumerate(lines, 1):
            print(f"处理第 {i} 个单词: {line[:50]}...")
            
            # 解析单词行
            parts = line.split(" ", 2)
            if len(parts) < 3:
                print(f"  跳过格式不正确的行: {line}")
                continue
            
            word = parts[0]
            pronunciation = parts[1]
            meaning = parts[2]
            
            # 创建文本图像
            try:
                img = create_text_image(word, pronunciation, meaning, width, height, font_size)
                
                # 将RGB转换为BGR (OpenCV格式)
                img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                
                # 写入多帧 (每个单词显示指定的帧数)
                for frame in range(frames_per_word):
                    out.write(img_bgr)
                
                print(f"  ✓ 已写入 {frames_per_word} 帧")
                
            except Exception as e:
                print(f"  ✗ 处理失败: {e}")
                continue
        
        print("\\n视频生成完成！")
        
    except Exception as e:
        print(f"生成视频时出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 释放资源
        out.release()
        cv2.destroyAllWindows()
        
        # 检查输出文件
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"\\n✓ 视频文件已保存: {output_file}")
            print(f"文件大小: {file_size:.2f} MB")
            print(f"视频时长: {total_duration} 秒")
            print(f"分辨率: {width}x{height}")
            print(f"帧率: {fps} fps")
        else:
            print("\\n✗ 视频文件生成失败")

def main():
    """主函数"""
    input_file = "Random_10_Words.txt"
    output_file = "word_video_opencv.mp4"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return
    
    # 检查OpenCV是否可用
    try:
        print(f"OpenCV版本: {cv2.__version__}")
    except:
        print("错误: OpenCV未安装，请运行: pip install opencv-python")
        return
    
    # 生成视频
    generate_word_video_opencv(
        input_file=input_file,
        output_file=output_file,
        font_size=70,
        total_duration=30,
        fps=24
    )

if __name__ == "__main__":
    main()
