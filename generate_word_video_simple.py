from moviepy.editor import TextClip, concatenate_videoclips
from moviepy.config import change_settings
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16\\magick.exe"})

def generate_word_video_simple(input_file, output_file="output.mp4", font_size=70, total_duration=30):
    """
    从TXT文件生成简单的单词视频
    """
    # 读取TXT文件
    with open(input_file, "r", encoding="utf-8") as file:
        lines = [line.strip() for line in file.readlines() if line.strip()][:10]

    clips = []
    duration_per_word = total_duration / len(lines)
    
    print(f"处理 {len(lines)} 个单词，每个单词时长: {duration_per_word:.2f} 秒")

    for i, line in enumerate(lines):
        print(f"处理第 {i+1} 个单词: {line[:30]}...")
        
        # 解析单词、音标和释义
        parts = line.split(" ", 2)
        if len(parts) < 3:
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]

        # 创建单个文本片段，包含所有信息
        text_content = f"{word}\n{pronunciation}\n{meaning}"
        
        try:
            clip = TextClip(
                text_content,
                fontsize=font_size,
                color="white",
                bg_color="black",
                size=(1920, 1080),
                method='caption'
            ).set_duration(duration_per_word)
            
            clips.append(clip)
            print(f"  片段创建成功")
            
        except Exception as e:
            print(f"  片段创建失败: {e}")
            continue

    if not clips:
        print("错误: 没有成功创建任何视频片段")
        return

    print(f"开始合并 {len(clips)} 个片段...")
    
    try:
        # 合并所有片段
        final_clip = concatenate_videoclips(clips, method="compose")
        print(f"最终视频时长: {final_clip.duration:.2f} 秒")
        
        print("开始写入视频文件...")
        final_clip.write_videofile(
            output_file, 
            fps=24, 
            codec="libx264",
            audio_codec="aac",
            temp_audiofile="temp-audio.m4a",
            remove_temp=True
        )
        print(f"视频已保存为: {output_file}")
        
    except Exception as e:
        print(f"视频合并或写入失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    input_file = "Random_10_Words.txt"
    output_file = "word_video_simple.mp4"
    generate_word_video_simple(input_file, output_file)
