from moviepy.editor import TextClip, concatenate_videoclips, ColorClip, CompositeVideoClip
from moviepy.config import change_settings
import numpy as np

# 设置ImageMagick路径
change_settings({"IMAGEMAGICK_BINARY": r"C:\\Program Files\\ImageMagick-7.1.2-Q16\\magick.exe"})

def create_word_video_clip(word, pronunciation, meaning, duration, width=1920, height=1080):
    """创建单个单词的视频片段"""
    
    # 创建黑色背景
    background = ColorClip(size=(width, height), color=(0, 0, 0), duration=duration)
    
    # 字体大小设置
    word_fontsize = 80
    pronunciation_fontsize = 60
    meaning_fontsize = 50
    
    try:
        # 创建单词文本片段
        word_clip = TextClip(
            word,
            fontsize=word_fontsize,
            color='white',
            font='Arial-Bold'
        ).set_position(('center', height*0.25)).set_duration(duration)
        
        # 创建音标文本片段
        pronunciation_clip = TextClip(
            pronunciation,
            fontsize=pronunciation_fontsize,
            color='yellow',
            font='Arial'
        ).set_position(('center', height*0.45)).set_duration(duration)
        
        # 创建释义文本片段
        meaning_clip = TextClip(
            meaning,
            fontsize=meaning_fontsize,
            color='lightblue',
            font='Arial'
        ).set_position(('center', height*0.65)).set_duration(duration)
        
        # 合成视频片段
        video_clip = CompositeVideoClip([
            background,
            word_clip,
            pronunciation_clip,
            meaning_clip
        ])
        
        return video_clip
        
    except Exception as e:
        print(f"创建复合片段失败，使用简单方案: {e}")
        
        # 简单方案：创建单一文本片段
        simple_text = f"{word}\\n\\n{pronunciation}\\n\\n{meaning}"
        simple_clip = TextClip(
            simple_text,
            fontsize=50,
            color='white',
            font='Arial'
        ).set_position('center').set_duration(duration)
        
        return CompositeVideoClip([background, simple_clip])

def generate_word_video_working(input_file, output_file="word_video_working.mp4", total_duration=30):
    """生成单词学习视频 - 工作版本"""
    
    print("🎬 开始生成单词学习视频...")
    
    # 读取单词文件
    try:
        with open(input_file, "r", encoding="utf-8") as file:
            lines = [line.strip() for line in file.readlines() if line.strip()][:10]
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False
    
    if not lines:
        print("❌ 错误: 文件中没有有效的单词数据")
        return False
    
    clips = []
    duration_per_word = total_duration / len(lines)
    
    print(f"📝 处理 {len(lines)} 个单词，每个单词显示 {duration_per_word:.2f} 秒")
    
    for i, line in enumerate(lines, 1):
        print(f"🔄 处理第 {i} 个单词: {line[:50]}...")
        
        # 解析单词行
        parts = line.split(" ", 2)
        if len(parts) < 3:
            print(f"⚠️  跳过格式不正确的行: {line}")
            continue
        
        word = parts[0]
        pronunciation = parts[1]
        meaning = parts[2]
        
        try:
            # 创建视频片段
            clip = create_word_video_clip(word, pronunciation, meaning, duration_per_word)
            clips.append(clip)
            print(f"✅ 片段 {i} 创建成功")
            
        except Exception as e:
            print(f"❌ 片段 {i} 创建失败: {e}")
            continue
    
    if not clips:
        print("❌ 错误: 没有成功创建任何视频片段")
        return False
    
    print(f"🔗 开始合并 {len(clips)} 个视频片段...")
    
    try:
        # 合并所有片段
        final_video = concatenate_videoclips(clips, method="compose")
        print(f"✅ 合并完成，总时长: {final_video.duration:.2f} 秒")
        
        # 设置视频属性
        final_video = final_video.set_fps(24)
        
        print(f"💾 正在保存视频到: {output_file}")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        # 写入视频文件 - 使用最兼容的参数
        final_video.write_videofile(
            output_file,
            codec='libx264',
            audio=False,  # 不包含音频
            verbose=False,
            logger=None
        )
        
        print(f"🎉 视频生成成功！")
        print(f"📁 输出文件: {output_file}")
        print(f"⏱️  视频时长: {final_video.duration:.2f} 秒")
        
        # 检查文件
        import os
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"📊 文件大小: {file_size:.2f} MB")
            return True
        else:
            print("❌ 视频文件未生成")
            return False
        
    except Exception as e:
        print(f"❌ 视频生成失败: {e}")
        
        # 尝试更简单的方法
        print("🔄 尝试使用更简单的参数...")
        try:
            final_video.write_videofile(
                output_file.replace('.mp4', '_simple.mp4'),
                verbose=False
            )
            print("✅ 使用简单参数生成成功！")
            return True
        except Exception as e2:
            print(f"❌ 简单方法也失败了: {e2}")
            
            # 最后的尝试：生成AVI格式
            print("🔄 最后尝试：生成AVI格式...")
            try:
                avi_file = output_file.replace('.mp4', '.avi')
                final_video.write_videofile(avi_file, codec='png')
                print(f"✅ AVI格式生成成功: {avi_file}")
                return True
            except Exception as e3:
                print(f"❌ 所有方法都失败了: {e3}")
                return False

def main():
    """主函数"""
    input_file = "Random_10_Words.txt"
    output_file = "word_video_working.mp4"
    
    print("=" * 60)
    print("🎯 单词学习视频生成器")
    print("=" * 60)
    
    # 检查输入文件
    import os
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件 {input_file} 不存在")
        return
    
    # 检查MoviePy
    try:
        import moviepy
        print(f"📦 MoviePy 版本: {moviepy.__version__}")
    except:
        print("❌ MoviePy 未安装")
        return
    
    # 生成视频
    success = generate_word_video_working(
        input_file=input_file,
        output_file=output_file,
        total_duration=30
    )
    
    if success:
        print("\\n🎊 任务完成！您的单词学习视频已生成。")
    else:
        print("\\n😞 视频生成失败，请检查错误信息。")
        print("\\n💡 建议:")
        print("1. 确保ImageMagick已正确安装")
        print("2. 尝试重新安装MoviePy: pip install --upgrade moviepy")
        print("3. 检查系统权限和磁盘空间")

if __name__ == "__main__":
    main()
