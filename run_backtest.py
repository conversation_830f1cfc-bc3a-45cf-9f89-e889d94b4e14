"""
运行BTC & ETH蜡烛图策略回测
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免Qt问题
import matplotlib.pyplot as plt
import seaborn as sns
from backtest_engine import BacktestEngine
from trading_config import load_config, ConfigPresets
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def run_comprehensive_backtest():
    """运行综合回测"""
    print("🚀 BTC & ETH 蜡烛图策略回测系统")
    print("=" * 60)
    
    # 回测参数
    symbols = ['BTC/USDT', 'ETH/USDT']
    start_date = '2024-01-01'
    end_date = '2025-01-01'  # 至今（2025年）
    initial_balance = 10000
    timeframe = '5m'  # 5分钟K线
    
    print(f"📊 回测参数:")
    print(f"   交易对: {', '.join(symbols)}")
    print(f"   时间范围: {start_date} 至 {end_date}")
    print(f"   时间框架: {timeframe}")
    print(f"   初始资金: ${initial_balance:,}")
    print()
    
    # 测试不同配置
    configs = {
        '保守型': 'conservative',
        '激进型': 'aggressive', 
        '剥头皮': 'scalping',
        '波段交易': 'swing'
    }
    
    results_summary = {}
    
    for config_name, config_type in configs.items():
        print(f"🔄 正在测试 {config_name} 配置...")
        print("-" * 40)
        
        try:
            # 加载配置
            config = load_config(config_type)
            config.initial_balance = initial_balance
            
            # 创建回测引擎
            engine = BacktestEngine(config)
            
            # 运行回测
            results = engine.run_backtest(symbols, start_date, end_date, timeframe)
            
            # 保存结果
            results_summary[config_name] = {
                'engine': engine,
                'results': results,
                'config': config
            }
            
            # 显示关键指标
            print(f"✅ {config_name} 回测完成:")
            print(f"   总收益率: {results['total_return']:.2f}%")
            print(f"   胜率: {results['win_rate']:.2f}%")
            print(f"   最大回撤: {results['max_drawdown']:.2f}%")
            print(f"   盈亏比: {results['profit_factor']:.2f}")
            print(f"   总交易次数: {results['total_trades']}")
            print()
            
        except Exception as e:
            print(f"❌ {config_name} 回测失败: {e}")
            print()
    
    return results_summary

def compare_strategies(results_summary):
    """比较不同策略的表现"""
    print("📈 策略表现对比")
    print("=" * 60)
    
    # 创建对比表格
    comparison_data = []
    
    for strategy_name, data in results_summary.items():
        results = data['results']
        comparison_data.append({
            '策略': strategy_name,
            '总收益率(%)': f"{results['total_return']:.2f}",
            '胜率(%)': f"{results['win_rate']:.2f}",
            '最大回撤(%)': f"{results['max_drawdown']:.2f}",
            '盈亏比': f"{results['profit_factor']:.2f}",
            '夏普比率': f"{results['sharpe_ratio']:.2f}",
            '交易次数': results['total_trades'],
            '最终资金($)': f"{results['final_balance']:.2f}"
        })
    
    df_comparison = pd.DataFrame(comparison_data)
    print(df_comparison.to_string(index=False))
    print()
    
    # 找出最佳策略
    best_return = max(results_summary.items(), key=lambda x: x[1]['results']['total_return'])
    best_sharpe = max(results_summary.items(), key=lambda x: x[1]['results']['sharpe_ratio'])
    best_winrate = max(results_summary.items(), key=lambda x: x[1]['results']['win_rate'])
    
    print("🏆 最佳表现:")
    print(f"   最高收益率: {best_return[0]} ({best_return[1]['results']['total_return']:.2f}%)")
    print(f"   最高夏普比率: {best_sharpe[0]} ({best_sharpe[1]['results']['sharpe_ratio']:.2f})")
    print(f"   最高胜率: {best_winrate[0]} ({best_winrate[1]['results']['win_rate']:.2f}%)")
    print()
    
    return df_comparison

def plot_strategy_comparison(results_summary):
    """绘制策略对比图表"""
    print("📊 正在生成对比图表...")
    
    # 准备数据
    strategies = list(results_summary.keys())
    returns = [data['results']['total_return'] for data in results_summary.values()]
    win_rates = [data['results']['win_rate'] for data in results_summary.values()]
    max_drawdowns = [data['results']['max_drawdown'] for data in results_summary.values()]
    sharpe_ratios = [data['results']['sharpe_ratio'] for data in results_summary.values()]
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('策略表现对比', fontsize=16, fontweight='bold')
    
    # 1. 总收益率对比
    colors = ['green' if x > 0 else 'red' for x in returns]
    bars1 = axes[0, 0].bar(strategies, returns, color=colors, alpha=0.7)
    axes[0, 0].set_title('总收益率对比 (%)')
    axes[0, 0].set_ylabel('收益率 (%)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, returns):
        height = bar.get_height()
        axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                       f'{value:.1f}%', ha='center', va='bottom' if height > 0 else 'top')
    
    # 2. 胜率对比
    bars2 = axes[0, 1].bar(strategies, win_rates, color='blue', alpha=0.7)
    axes[0, 1].set_title('胜率对比 (%)')
    axes[0, 1].set_ylabel('胜率 (%)')
    axes[0, 1].grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, win_rates):
        height = bar.get_height()
        axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{value:.1f}%', ha='center', va='bottom')
    
    # 3. 最大回撤对比
    bars3 = axes[1, 0].bar(strategies, max_drawdowns, color='red', alpha=0.7)
    axes[1, 0].set_title('最大回撤对比 (%)')
    axes[1, 0].set_ylabel('最大回撤 (%)')
    axes[1, 0].grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, max_drawdowns):
        height = bar.get_height()
        axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + 0.2,
                       f'{value:.1f}%', ha='center', va='bottom')
    
    # 4. 夏普比率对比
    colors = ['green' if x > 0 else 'red' for x in sharpe_ratios]
    bars4 = axes[1, 1].bar(strategies, sharpe_ratios, color=colors, alpha=0.7)
    axes[1, 1].set_title('夏普比率对比')
    axes[1, 1].set_ylabel('夏普比率')
    axes[1, 1].grid(True, alpha=0.3)
    
    for bar, value in zip(bars4, sharpe_ratios):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + (0.05 if height > 0 else -0.1),
                       f'{value:.2f}', ha='center', va='bottom' if height > 0 else 'top')
    
    # 调整布局
    plt.tight_layout()
    plt.show()
    
    return fig

def analyze_pattern_performance(results_summary):
    """分析蜡烛图形态表现"""
    print("🕯️ 蜡烛图形态表现分析")
    print("=" * 60)
    
    all_trades = []
    
    # 收集所有交易数据
    for strategy_name, data in results_summary.items():
        engine = data['engine']
        for trade in engine.trades:
            trade_copy = trade.copy()
            trade_copy['strategy'] = strategy_name
            all_trades.append(trade_copy)
    
    if not all_trades:
        print("没有交易数据可分析")
        return
    
    trades_df = pd.DataFrame(all_trades)
    
    # 按形态分析
    pattern_analysis = trades_df.groupby('pattern_type').agg({
        'pnl': ['count', 'sum', 'mean'],
        'return_pct': 'mean'
    }).round(2)
    
    pattern_analysis.columns = ['交易次数', '总盈亏($)', '平均盈亏($)', '平均收益率(%)']
    
    # 计算胜率
    pattern_winrate = trades_df.groupby('pattern_type').apply(
        lambda x: (x['pnl'] > 0).sum() / len(x) * 100
    ).round(2)
    pattern_analysis['胜率(%)'] = pattern_winrate
    
    print("各蜡烛图形态表现:")
    print(pattern_analysis.to_string())
    print()
    
    # 找出最佳形态
    best_pattern = pattern_analysis['总盈亏($)'].idxmax()
    best_winrate_pattern = pattern_analysis['胜率(%)'].idxmax()
    
    print(f"🏆 最佳形态:")
    print(f"   最高盈利: {best_pattern} (总盈亏: ${pattern_analysis.loc[best_pattern, '总盈亏($)']:.2f})")
    print(f"   最高胜率: {best_winrate_pattern} (胜率: {pattern_analysis.loc[best_winrate_pattern, '胜率(%)']:.2f}%)")
    print()
    
    return pattern_analysis

def generate_detailed_report(results_summary):
    """生成详细报告"""
    print("📋 生成详细回测报告")
    print("=" * 60)
    
    report_lines = []
    report_lines.append("BTC & ETH 蜡烛图策略回测报告")
    report_lines.append("=" * 50)
    report_lines.append(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"回测期间: 2023-01-01 至 2024-01-01")
    report_lines.append(f"交易对: BTC/USDT, ETH/USDT")
    report_lines.append(f"初始资金: $10,000")
    report_lines.append("")
    
    # 各策略详细结果
    for strategy_name, data in results_summary.items():
        results = data['results']
        config = data['config']
        
        report_lines.append(f"【{strategy_name}策略】")
        report_lines.append("-" * 30)
        report_lines.append(f"配置参数:")
        report_lines.append(f"  - 最大仓位比例: {config.max_position_size*100:.1f}%")
        report_lines.append(f"  - 单笔风险比例: {config.risk_per_trade*100:.1f}%")
        report_lines.append(f"  - 最小置信度: {config.min_confidence:.2f}")
        report_lines.append(f"  - 风险回报比: {config.default_risk_reward_ratio:.1f}")
        report_lines.append("")
        
        report_lines.append(f"回测结果:")
        report_lines.append(f"  - 总收益率: {results['total_return']:.2f}%")
        report_lines.append(f"  - 最终资金: ${results['final_balance']:.2f}")
        report_lines.append(f"  - 总交易次数: {results['total_trades']}")
        report_lines.append(f"  - 胜率: {results['win_rate']:.2f}%")
        report_lines.append(f"  - 盈亏比: {results['profit_factor']:.2f}")
        report_lines.append(f"  - 最大回撤: {results['max_drawdown']:.2f}%")
        report_lines.append(f"  - 夏普比率: {results['sharpe_ratio']:.2f}")
        report_lines.append(f"  - 平均盈利: ${results['avg_win']:.2f}")
        report_lines.append(f"  - 平均亏损: ${results['avg_loss']:.2f}")
        report_lines.append("")
    
    # 保存报告
    report_content = "\n".join(report_lines)
    
    with open('backtest_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✅ 详细报告已保存至 backtest_report.txt")
    print()
    
    return report_content

def main():
    """主函数"""
    print("🎯 开始运行BTC & ETH蜡烛图策略回测")
    print()
    
    try:
        # 1. 运行综合回测
        results_summary = run_comprehensive_backtest()
        
        if not results_summary:
            print("❌ 没有成功的回测结果")
            return
        
        # 2. 策略对比
        comparison_df = compare_strategies(results_summary)
        
        # 3. 绘制对比图表
        plot_strategy_comparison(results_summary)
        
        # 4. 形态表现分析
        pattern_analysis = analyze_pattern_performance(results_summary)
        
        # 5. 生成详细报告
        generate_detailed_report(results_summary)
        
        # 6. 绘制最佳策略的详细图表（如果有交易的话）
        if any(data['results']['total_trades'] > 0 for data in results_summary.values()):
            best_strategy = max(results_summary.items(), 
                              key=lambda x: x[1]['results']['total_return'])
            
            print(f"📊 正在绘制最佳策略 ({best_strategy[0]}) 的详细图表...")
            best_strategy[1]['engine'].plot_results()
        else:
            print("📊 没有交易数据，跳过图表绘制")
        
        print("🎉 回测完成！")
        print()
        print("📁 生成的文件:")
        print("   - backtest_report.txt (详细报告)")
        print("   - 多个图表窗口 (策略对比和详细分析)")
        
    except Exception as e:
        print(f"❌ 回测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
