"""
BTC & ETH 蜡烛图交易策略配置文件
"""

from dataclasses import dataclass
from typing import Dict, List

@dataclass
class TradingConfig:
    """交易配置"""
    
    # 基本设置
    initial_balance: float = 10000.0
    symbols: List[str] = None
    timeframes: List[str] = None
    
    # 风险管理
    max_position_size: float = 0.02  # 单笔交易最大仓位比例 (2%)
    max_daily_loss: float = 0.05     # 日最大亏损比例 (5%)
    risk_per_trade: float = 0.02     # 单笔交易风险比例 (2%)
    max_positions: int = 3           # 最大同时持仓数量
    
    # 蜡烛图形态检测
    min_confidence: float = 0.6      # 最小形态置信度
    min_body_ratio: float = 0.1      # 最小实体比例
    doji_threshold: float = 0.05     # 十字星判定阈值
    
    # 技术指标参数
    rsi_period: int = 14
    rsi_oversold: float = 30
    rsi_overbought: float = 70
    sma_short: int = 20
    sma_long: int = 50
    
    # 支撑阻力识别
    support_resistance_window: int = 20
    price_tolerance: float = 0.02    # 价格容忍度 (2%)
    
    # 止损止盈设置
    default_stop_loss_pct: float = 0.02   # 默认止损比例 (2%)
    default_risk_reward_ratio: float = 2.0 # 默认风险回报比
    trailing_stop_enabled: bool = False   # 是否启用移动止损
    trailing_stop_pct: float = 0.01       # 移动止损比例 (1%)
    
    # 交易时间设置
    trading_hours: Dict[str, str] = None  # 交易时间段
    avoid_news_events: bool = True        # 是否避开重大新闻事件
    
    # 市场环境过滤
    min_volume_ratio: float = 0.8         # 最小成交量比例
    max_spread_pct: float = 0.001         # 最大买卖价差比例
    volatility_threshold: float = 0.05    # 波动率阈值
    
    def __post_init__(self):
        """初始化后处理"""
        if self.symbols is None:
            self.symbols = ['BTC/USDT', 'ETH/USDT']
        
        if self.timeframes is None:
            self.timeframes = ['1h', '4h', '1d']
        
        if self.trading_hours is None:
            self.trading_hours = {
                'start': '00:00',
                'end': '23:59',
                'timezone': 'UTC'
            }

# 预设配置
class ConfigPresets:
    """配置预设"""
    
    @staticmethod
    def conservative() -> TradingConfig:
        """保守型配置"""
        return TradingConfig(
            max_position_size=0.01,
            max_daily_loss=0.03,
            risk_per_trade=0.01,
            min_confidence=0.7,
            default_risk_reward_ratio=3.0,
            max_positions=2
        )
    
    @staticmethod
    def aggressive() -> TradingConfig:
        """激进型配置"""
        return TradingConfig(
            max_position_size=0.05,
            max_daily_loss=0.10,
            risk_per_trade=0.03,
            min_confidence=0.5,
            default_risk_reward_ratio=1.5,
            max_positions=5
        )
    
    @staticmethod
    def scalping() -> TradingConfig:
        """剥头皮配置"""
        return TradingConfig(
            timeframes=['5m', '15m', '1h'],
            max_position_size=0.03,
            risk_per_trade=0.005,
            min_confidence=0.6,
            default_risk_reward_ratio=1.0,
            trailing_stop_enabled=True,
            max_positions=3
        )
    
    @staticmethod
    def swing_trading() -> TradingConfig:
        """波段交易配置"""
        return TradingConfig(
            timeframes=['4h', '1d', '3d'],
            max_position_size=0.04,
            risk_per_trade=0.02,
            min_confidence=0.7,
            default_risk_reward_ratio=3.0,
            max_positions=2
        )

# 交易对特定配置
BTC_CONFIG = {
    'min_trade_amount': 0.001,
    'tick_size': 0.01,
    'volatility_multiplier': 1.0,
    'correlation_symbols': ['ETH/USDT', 'BNB/USDT'],
    'support_levels': [40000, 45000, 50000, 55000, 60000],
    'resistance_levels': [42000, 47000, 52000, 57000, 62000]
}

ETH_CONFIG = {
    'min_trade_amount': 0.01,
    'tick_size': 0.01,
    'volatility_multiplier': 1.2,
    'correlation_symbols': ['BTC/USDT', 'BNB/USDT'],
    'support_levels': [2500, 3000, 3500, 4000, 4500],
    'resistance_levels': [2700, 3200, 3700, 4200, 4700]
}

# 形态权重配置
PATTERN_WEIGHTS = {
    'hammer': {
        'base_weight': 1.0,
        'trend_multiplier': {'downtrend': 1.5, 'uptrend': 0.5, 'sideways': 0.8},
        'volume_multiplier': 1.2,
        'support_resistance_multiplier': 1.3
    },
    'shooting_star': {
        'base_weight': 1.0,
        'trend_multiplier': {'uptrend': 1.5, 'downtrend': 0.5, 'sideways': 0.8},
        'volume_multiplier': 1.2,
        'support_resistance_multiplier': 1.3
    },
    'doji': {
        'base_weight': 0.8,
        'trend_multiplier': {'uptrend': 1.0, 'downtrend': 1.0, 'sideways': 1.2},
        'volume_multiplier': 1.0,
        'support_resistance_multiplier': 1.1
    },
    'bullish_engulfing': {
        'base_weight': 1.3,
        'trend_multiplier': {'downtrend': 1.4, 'uptrend': 0.7, 'sideways': 1.0},
        'volume_multiplier': 1.3,
        'support_resistance_multiplier': 1.4
    },
    'bearish_engulfing': {
        'base_weight': 1.3,
        'trend_multiplier': {'uptrend': 1.4, 'downtrend': 0.7, 'sideways': 1.0},
        'volume_multiplier': 1.3,
        'support_resistance_multiplier': 1.4
    },
    'morning_star': {
        'base_weight': 1.5,
        'trend_multiplier': {'downtrend': 1.6, 'uptrend': 0.6, 'sideways': 0.9},
        'volume_multiplier': 1.4,
        'support_resistance_multiplier': 1.5
    },
    'evening_star': {
        'base_weight': 1.5,
        'trend_multiplier': {'uptrend': 1.6, 'downtrend': 0.6, 'sideways': 0.9},
        'volume_multiplier': 1.4,
        'support_resistance_multiplier': 1.5
    }
}

# 市场环境配置
MARKET_CONDITIONS = {
    'bull_market': {
        'bullish_pattern_multiplier': 1.3,
        'bearish_pattern_multiplier': 0.7,
        'min_confidence_adjustment': -0.1,
        'preferred_timeframes': ['1h', '4h']
    },
    'bear_market': {
        'bullish_pattern_multiplier': 0.7,
        'bearish_pattern_multiplier': 1.3,
        'min_confidence_adjustment': -0.1,
        'preferred_timeframes': ['4h', '1d']
    },
    'sideways_market': {
        'bullish_pattern_multiplier': 1.0,
        'bearish_pattern_multiplier': 1.0,
        'min_confidence_adjustment': 0.1,
        'preferred_timeframes': ['1h', '4h']
    },
    'high_volatility': {
        'stop_loss_multiplier': 1.5,
        'position_size_multiplier': 0.7,
        'min_confidence_adjustment': 0.2
    },
    'low_volatility': {
        'stop_loss_multiplier': 0.8,
        'position_size_multiplier': 1.2,
        'min_confidence_adjustment': -0.1
    }
}

# 新闻事件配置
NEWS_EVENTS = {
    'high_impact': [
        'FOMC Meeting',
        'CPI Release',
        'NFP Release',
        'Bitcoin ETF News',
        'Ethereum Upgrade'
    ],
    'medium_impact': [
        'GDP Release',
        'Unemployment Rate',
        'Crypto Regulation News'
    ],
    'avoid_trading_minutes': {
        'before': 30,
        'after': 60
    }
}

# 回测配置
BACKTEST_CONFIG = {
    'start_date': '2023-01-01',
    'end_date': '2024-01-01',
    'initial_balance': 10000,
    'commission': 0.001,  # 0.1%
    'slippage': 0.0005,   # 0.05%
    'benchmark': 'BTC/USDT',
    'metrics': [
        'total_return',
        'sharpe_ratio',
        'max_drawdown',
        'win_rate',
        'profit_factor',
        'avg_trade_duration'
    ]
}

# 实盘交易配置
LIVE_TRADING_CONFIG = {
    'paper_trading': True,  # 是否为模拟交易
    'exchange': 'binance',
    'api_key': '',
    'api_secret': '',
    'testnet': True,
    'order_type': 'market',  # 'market' or 'limit'
    'max_retries': 3,
    'retry_delay': 1,  # 秒
    'heartbeat_interval': 30,  # 秒
    'log_level': 'INFO'
}

# 通知配置
NOTIFICATION_CONFIG = {
    'enabled': True,
    'channels': ['console', 'file'],  # 'console', 'file', 'email', 'telegram'
    'email': {
        'smtp_server': '',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'to_address': ''
    },
    'telegram': {
        'bot_token': '',
        'chat_id': ''
    },
    'log_file': 'trading_log.txt',
    'notify_on': [
        'trade_executed',
        'position_closed',
        'stop_loss_hit',
        'take_profit_hit',
        'error_occurred'
    ]
}

# 默认配置
DEFAULT_CONFIG = TradingConfig()

# 配置验证函数
def validate_config(config: TradingConfig) -> bool:
    """验证配置的有效性"""
    errors = []
    
    if config.max_position_size <= 0 or config.max_position_size > 1:
        errors.append("max_position_size 必须在 0 到 1 之间")
    
    if config.max_daily_loss <= 0 or config.max_daily_loss > 1:
        errors.append("max_daily_loss 必须在 0 到 1 之间")
    
    if config.min_confidence < 0 or config.min_confidence > 1:
        errors.append("min_confidence 必须在 0 到 1 之间")
    
    if config.default_risk_reward_ratio <= 0:
        errors.append("default_risk_reward_ratio 必须大于 0")
    
    if not config.symbols:
        errors.append("symbols 不能为空")
    
    if not config.timeframes:
        errors.append("timeframes 不能为空")
    
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

# 配置加载函数
def load_config(config_type: str = 'default') -> TradingConfig:
    """加载指定类型的配置"""
    config_map = {
        'default': DEFAULT_CONFIG,
        'conservative': ConfigPresets.conservative(),
        'aggressive': ConfigPresets.aggressive(),
        'scalping': ConfigPresets.scalping(),
        'swing': ConfigPresets.swing_trading()
    }
    
    config = config_map.get(config_type, DEFAULT_CONFIG)
    
    if validate_config(config):
        return config
    else:
        raise ValueError(f"配置 '{config_type}' 验证失败")

if __name__ == "__main__":
    # 测试配置
    for config_name in ['default', 'conservative', 'aggressive', 'scalping', 'swing']:
        try:
            config = load_config(config_name)
            print(f"✓ {config_name} 配置加载成功")
        except ValueError as e:
            print(f"✗ {config_name} 配置加载失败: {e}")
